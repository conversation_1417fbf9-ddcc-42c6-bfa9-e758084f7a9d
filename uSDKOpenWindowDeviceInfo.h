//
//  uSDKOpenWindowDeviceInfo.h
//  uSDK
//
//  Created by 郭永峰 on 2025/3/4.
//  Copyright © 2025 haier. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface uSDKOpenWindowDeviceInfo : NSObject

/// 设备id
@property (nonatomic, copy) NSString *deviceId;

/// 模块类型
@property (nonatomic, copy) NSString *moduleType;

/// 设备typeId
@property (nonatomic, copy) NSString *typeId;
@end

NS_ASSUME_NONNULL_END
