//
//  uSDKDeviceInfo.h
//  uSDK
//
//  Created by liugn on 15/12/11.
//  Copyright © 2015年 haier. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "uSDKConstantInfo.h"
#import "uSDKFaultInformation.h"
#import "uTrace.h"
/**
 *	设备连接状态
 */
typedef NS_ENUM(NSInteger,uSDKDeviceState) {
    /**
     *  未连接，未订阅设备的连接状态为未连接，只要设备订阅成功，则不会是该状态
     */
    uSDKDeviceStateUnconnect = 0,
    /**
     *  离线
     */
    uSDKDeviceStateOffline,
    /**
     *  连接中
     */
    uSDKDeviceStateConnecting,
    /**
     *  连接成功
     */
    uSDKDeviceStateConnected,
    /**
     *  就绪
     */
    uSDKDeviceStateReady,
};

/*
 * 废弃下面三个字符串常量
 *  @deprecated 5.4.0
 */
static NSString *const APP_PROT_STD = @"APP_PROT_STD";
static NSString *const APP_PROT_SIXID = @"APP_PROT_SIXID";
static NSString *const DEV_PROT_STD = @"DEV_PROT_STD";

/**
 *	设备协议类型
 */
typedef NS_ENUM(NSInteger,uSDKAppProtocolType) {
    /**
     *  协议类型--标准
     */
    uSDKProtocolTypeSTD,
    /**
     *  协议类型--六位码
     */
    uSDKProtocolTypeSIXID,
};


/**
 *	设备安全类型
 */
typedef NS_ENUM(NSInteger,uSDKDeviceSecurity) {
    /**
     *  安全性未知设备
     */
    uSDKDeviceSecurityUnknown = 0,
    /**
     *  安全设备
     */
    uSDKDeviceSecuritySafe = 1,
    /**
     *  非安全设备
     */
    uSDKDeviceSecurityUnsafe = 2,
};

/**
 *	设备网络类型
 */
typedef NS_ENUM(NSInteger,uSDKDeviceNetTypeConst) {
    /**
     *  远程
     */
    NET_TYPE_REMOTE = 0,
    /**
     *  本地
     */
    NET_TYPE_LOCAL = 1,
    /**
     *  蓝牙，5.3.0添加
     */
    NET_TYPE_BLE = 2,
    /**
     *  Mesh网关
     *  @since 7.0.0
     */
    NET_TYPE_MESH_GATEWAY = 3,
    /**
     *  BLE Mesh
     *  @since 7.0.0
     */
    NET_TYPE_BLE_MESH = 4,

} ;


/**
 设备支持的配置方式
 */
typedef NS_OPTIONS(NSUInteger, uSDKDeviceConfigType) {
    /**
     BLE配置方式，调用uSDKBinding.bindDeviceByBLE接口进行配置绑定
     */
    uSDKDeviceConfigTypeBLE = (1UL << 0),
    /**
     纯BLE配置方式，调用uSDKBinding.bindPureBLEDevice接口进行配置绑定
     @since 5.4.0
     */
    uSDKDeviceConfigTypePureBLE = (1UL << 1) ,
    /**
     极路由免密配置
     @deprecated 5.4.0
     */
    uSDKDeviceConfigTypeHiRouter = (1UL << 2),
    /**
     BLEMesh配置方式，调用uSDKBinding.bindBLEMeshDevice接口进行配置绑定
     @since 7.0.0
     */
    uSDKDeviceConfigTypeBLEMesh = (1UL << 3),
    /**
     新直连配置方式，调用uSDKBinding.bindNewDirectLinkDeviceWithVerificationCodeBindInfo接口进行配置绑定
     @since 6.0.0
     */
    uSDKDeviceConfigTypeNewDirectLink = (1UL << 4),
    /**
     蓝牙广播绑定方式，调用uSDKBinding.bindBLEADVDevice接口进行配置绑定
     @since 8.0.0
     */
    uSDKDeviceConfigTypeBLEADV = (1UL << 5),
    /**
     softAp配置方式，调用uSDKBinding.bindDeviceBySoftAp接口进行配置绑定
     @since 8.0.0
     */
    uSDKDeviceConfigTypeSoftAP = (1UL << 6),
    /**
     smartlink配置方式，调用uSDKBinding.bindDeviceBySmartLink接口进行配置绑定
     @since 8.6.0
     */
    uSDKDeviceConfigTypeSmartlink = (1UL << 7),
    /**
     无网先配，调用uSDKBinding.bindDeviceWithoutWifi接口进行先绑定
     @since 8.6.0
     */
    uSDKDeviceConfigTypeBindWithoutWifi = (1UL << 8),
    /**
     管理帧配网,自发现融合先添加的变量，如果没有用再去掉
     */
    uSDKDeviceConfigTypeManageFrame = (1UL << 9),
    /**
     云直连绑定
     */
    uSDKDeviceConfigTypeCloudDirectLink = (1UL << 10),
};

/**
 新直连绑定设备端校验方式
 @since 6.0.0
 */
typedef NS_ENUM(NSUInteger, uSDKNewDirectLinkVerificationMethod) {
    /**
    验证码校验方式
    @since 6.0.0
    */
    uSDKNewDirectLinkVerificationCode = 0,
    /**
    手动确认校验方式
    @since 6.0.0
    */
    uSDKNewDirectLinkVerificationManualConfirm = 1,
    /**
    非直连无验证方式
    @since 8.12.0
    */
    uSDKNewDirectLinkVerificationWithoutVerification = 2,
};

/**
 设备当前可配置状态
 @since 6.0.0
 */
typedef NS_ENUM(NSUInteger, uSDKDeviceConfigState) {
    /**
     设备当前状态已配置
     @since 6.0.0
     */
    uSDKDeviceConfigStateConfigured,
    /**
     设备当前状态可配置
     @since 6.0.0
     */
    uSDKDeviceConfigStateConfigurable,
    /**
     设备当前状态可触发进配置
     @since 6.0.0
     */
    uSDKDeviceConfigStateTriggerConfigurable,
    /**
    设备当前状态已绑定可配网
     */
    uSDKDeviceConfigStateUpRouterable,
};

/**
 设备配置完成后支持的绑定方式
 */
typedef NS_ENUM(NSUInteger, uSDKDeviceBindType) {
    /**
     手机绑定
     */
    uSDKDeviceBindTypePhoneBind,
    /**
     设备自绑定
     */
    uSDKDeviceBindTypeDeviceBind
};


/**
 模块升级进度阶段
 */
typedef NS_ENUM(NSUInteger, uSDKOTAStatus) {
    /**
     发送升级包
     */
    uSDKOTAStatusSendUpgradePackage,
    /**
     模块重启
     */
    uSDKOTAStatusModuleRestart
};

/**
 设备的通道能力
 @since 6.0.0
 */
typedef NS_OPTIONS(NSUInteger, uSDKDeviceChannel) {
    /**
     WiFi通道能力
     */
    uSDKDeviceChannelWiFi = (1UL << 0),
    /**
     BLE通道能力
     */
    uSDKDeviceChannelBLE = (1UL << 1),
    /**
     BLE MESH通道能力
     */
    uSDKDeviceChannelBLEMesh = (1UL << 2),
    /**
     BLE 广播通道能力
     */
    uSDKDeviceChannelBLEADV = (1UL << 3),
    
    /**
     zigBee3.0通道能力
     */
    uSDKDeviceChannelZigBee = (1UL << 4),
};

/**
 设备的可控状态
 @since 8.10.0
 */
typedef NS_ENUM(NSUInteger, uSDKDeviceControlState) {
    /**
     无可控状态（非快连3.0设备或者快连3.0设备未收到可控标志）
     */
    uSDKDeviceControlStateNone,
    /**
     可控制
     */
    uSDKDeviceControlStateControllable,
    /**
     靠近可控制
     */
    uSDKDeviceControlStateNearControllable,
    /**
     可控制且设备侧鉴权通过
     */
    uSDKDeviceControlStateControllableAndAuthorized,
    /**
     快连新流程
     */
    uSDKDeviceControlStateControllableNewFlow

};

/**
 设备WiFi频段
 */
typedef NS_ENUM(NSUInteger, uSDKDeviceWiFiFreqBrand) {
    /**
     支持2.4G
     */
    uSDKDeviceWiFiFreqBrand24G = 0,
    /**
     支持2.4G 和 5G
     */
    uSDKDeviceWiFiFreqBrand24GAnd5G,
};



@class uSDKErrorInfo,uSDKDeviceAttribute,uSDKAPInfo;
@protocol uSDKDeviceDelegate,uSDKDeviceInfoDelegate;

/**
 *  家电设备信息类，包括家电设备的基本属性信息。
 */
@interface uSDKDeviceInfo : NSObject{
    NSString* _deviceID;
    NSString* _uplusID;
    BOOL _isRemoteDeviceOnline;
    uSDKDeviceState _state;
    BOOL _isSubscribed;
    
    uSDKDeviceNetTypeConst _netType;
    uSDKDeviceTypeConst _type;
    NSInteger _middleType;
    NSString* _specialId;
    uSDKAppProtocolType _protocolType;
    BOOL _isComplexDevice;
}

/**
 *  设备ID；
 */
@property (atomic, copy ,readonly) NSString* deviceID;

/**
 旧的设备ID
 @brief 先绑后配接口返回的设备对象此字段有值，存储绑定之前的设备ID
 @since 9.7.0
*/
@property (nonatomic, copy ,readonly) NSString* oldDeviceID;


/// 设备的机器id，wifi&ble设备为wifi mac，ble设备为BDAddress。
@property (nonatomic, copy ,readonly) NSString* machineID;

/**
 设备名称
 示例： U-AC12A7
 */
@property (nonatomic, copy, readonly) NSString *name;

/**
 设备绑定名称
 示例：电热水器  或  我的设备o003
 @since 9.7.0
 */
@property (nonatomic, copy, readonly) NSString *deviceName;

/**
 设备功能集
 示例： PID_AAA9H1005
 @since 10.0.0
 */
@property (nonatomic, copy, readonly) NSString *pid;

/**
 设备是否支持仅配网
 @since 10.0.0
 */
@property (nonatomic, assign, readonly) NSInteger upRouter;

/**
 长期离线天数
 @since 10.0.0
 */
@property (nonatomic, assign, readonly) NSInteger offlineDays;

/**
 是否支持先绑后配时配置域名
 @since 10.4.0
 */
@property (nonatomic, assign, readonly) BOOL suppDNConfig;

/**
 是否支持扫描获取周边wifi列表
 @since 10.8.0
 */
@property (nonatomic, assign, readonly) BOOL scanWifiListAbility;

/**
 设备离线原因错误码
 @since 10.8.0
 */
@property (nonatomic, assign, readonly) NSInteger offlineCode;

/**
 *  设备安全性
 */
@property (nonatomic, assign ,readonly) uSDKDeviceSecurity security;

@property (nonatomic, assign ,readonly) NSInteger securityVersion;

/**
 *  设备应用协议类型
 */
@property (nonatomic, assign ,readonly) uSDKAppProtocolType protocolType;

/**
 *  设备遵守的E++协议版本号；
 */
@property (nonatomic, strong ,readonly) NSString* eProtocolVer;

/**
 *  是否是复杂设备
 */
@property (nonatomic, assign, readonly) BOOL isComplexDevice;

/**
 *  设备IP；
 */
@property (nonatomic, copy ,readonly) NSString* ip;

/**
 *  设备端口；
 */
@property (nonatomic, assign, readonly) NSInteger port;

/**
 *  获取设备状态。
 */
@property (nonatomic, assign, readonly) uSDKDeviceState state;


/**
 *  设备是否已被订阅；
 */
@property (nonatomic, assign ,readonly) BOOL isSubscribed;


/**
 设备是否自动连接蓝牙
 @brief 该参数的值由云端返回。云端会根据设备白名单的配置信息，来设置该参数的值。
 
 @since 9.6.0
 */
@property (nonatomic, assign, readonly) NSInteger isAutoConnect;
/**
 *  设备的类型唯一识别码，用来唯一标识设备类型；
 *  示例：2054a0000426cb2410114a18df53e500000018dbc685327c45f72cbd6d111e40
 */
@property (nonatomic, copy ,readonly) NSString* uplusID;

/**
 *  设备大类分类，设备按照大类通常分类为：洗衣机、冰箱、柜机空调、冰箱酒柜等。详细请见uSDKDeviceTypeConst枚举定义；
 */
@property (nonatomic, assign ) uSDKDeviceTypeConst type;

/**
 *  设备中类, 0表示无效
 */
@property (nonatomic, assign ,readonly) NSInteger middleType;

/**
 *  设备专用号，从typeId中解析得到
 *  例如： 4a18df53e5000
 */
@property (nonatomic, strong ,readonly) NSString* specialId;

/**
 *  设备的网络类型。网络类型是指本地、远程、蓝牙，详细请见uSDKDeviceNetTypeConst枚举定义；
 */
@property (nonatomic, assign ,readonly) uSDKDeviceNetTypeConst netType;

/**
 远程设备是否在线
 */
@property (nonatomic, assign ,readonly) BOOL isRemoteDeviceOnline;

/**
 是否支持免密配置
 */
@property (nonatomic, assign, readonly) BOOL supportNoPwdConfig;

/**
 设备绑定方式
 */
@property (nonatomic, assign, readonly) uSDKDeviceBindType bindType;

/**
 设备支持的配置方式，用于自发现设备
 */
@property (nonatomic, assign, readonly) uSDKDeviceConfigType configType;

/**
 设备当前的可配置状态
 @since 6.0.0
*/
@property (nonatomic, assign, readonly) uSDKDeviceConfigState configState;


/**
 设备是否支持 WIFI&BLE 批量绑定
 @since 9.13.0
*/
@property (nonatomic, assign, readonly) BOOL batchBindSupport;

/**
 *  设备wifi的平台信息,如果此字段值为mqttUGWAuth 则表示设备支持授权；
 */
@property (nonatomic, strong ,readonly) NSString* softwareType;

/**
 *  设备模块的软件版本号；
 */
@property (nonatomic, copy, readonly) NSString* smartLinkSoftwareVersion;

/**
 *  设备模块的硬件版本号；
 */
@property (nonatomic, copy, readonly) NSString* smartLinkHardwareVersion;

/**
 蓝牙广播包中的RSSI，用于判断信号强度
 负值，越接近0信号越强
 @since 5.4.0
 */
@property (nonatomic, assign) NSInteger RSSI;

/**
 *  设备代理，用于接收回调消息（设备状态变化、属性变化、报警等）
 * @since 9.11.0
 */
@property (nonatomic, weak) id<uSDKDeviceInfoDelegate> infoDelegate;

/**
 信号强度等级
 取值范围 1 ~ 5，   5 优，4 良，3 中，2 差，1 极差   默认值0代表 无效
 @since 9.11.0
 */
@property (nonatomic, assign, readonly) NSInteger BLESignalLevel;

/**
 信号强度等级
 取值范围 1 ~ 5，   5 优，4 良，3 中，2 差，1 极差   默认值0代表 无效
 @since 9.11.0
 */
@property (nonatomic, assign, readonly) NSInteger QCBLESignalLevel;

/**
 产品编码
 示例：AAA9H1005
 @since 5.4.0
 */
@property (nonatomic, copy, readonly) NSString *productCode;

/**
 标识设备是否被绑定了
 @since 5.4.1
 */
@property (nonatomic, assign, readonly) BOOL isBound;

/**
 模块是否需要升级
 @since 5.5.0
 */
@property (nonatomic, assign, readonly) BOOL isModuleNeedOTA;

/**
 设备支持的通道能力
 1. (channel & uSDKDeviceChannelBLEMesh) && !(channel & uSDKDeviceChannelBLE)== 1     //表示为mesh设备
 2. ((channel & uSDKDeviceChannelBLEMesh) && (channel & uSDKDeviceChannelWiFi) && (channel & uSDKDeviceChannelBLE)) == 1    //表示为mesh网关设备
 3. (!(channel & uSDKDeviceChannelWiFi) && (channel & uSDKDeviceChannelBLE) && !(channel & uSDKDeviceChannelBLEMesh) && (channel & uSDKDeviceChannelBLEADV)) == 1  //表示为蓝牙门锁类设备
 4.(channel & uSDKDeviceChannelBLEADV) && !((channel & uSDKDeviceChannelBLE)) ==1 //表示蓝牙体脂秤设备
 @since 6.0.0
 */
@property (nonatomic, assign, readonly) uSDKDeviceChannel channel;

/**
 是否为组设备
 @since 6.0.0
 */
@property (nonatomic, assign, readonly) BOOL isGroup;

/**
 新直连绑定设备的校验方式
 当configType & uSDKDeviceConfigTypeNewDirectLink == 1时，该属性有效
 @since 6.0.0
 */
@property (nonatomic, assign, readonly) uSDKNewDirectLinkVerificationMethod verificationMethod;

/**
 设备故障信息
 @since 6.1.0
*/
@property (nonatomic, strong, readonly) uSDKFaultInformation* faultInfo;

/**
 设备热点名称
 示例：U-AC12A7-A91R6
 @since 8.0.0
 */
@property (nonatomic, copy ,readonly) NSString* hotSpotName;

/**
 应用类型名称
 示例：AC，
 因历史原因， 在自发现上云中，这个字段返回的是中文名称，如 洗碗机，空调。
 目前自发现上云还未接入，后期需要统一格式。
 @since 8.6.0
*/
@property (nonatomic, copy, readonly) NSString* apptypeName;

/**
 设备支持的所有配置方式
 @since 8.6.0
 */
@property (nonatomic, assign, readonly) uSDKDeviceConfigType allConfigTypes;

/**
 设备的可控状态
 未收到可控状态的快连3.0设备或者非快连3.0设备值是uSDKDeviceControlStateNone
 @since 8.10.0
 */
@property (nonatomic, assign, readonly) uSDKDeviceControlState controlState;

/**
 设备WiFi频段
 @since 8.10.0
 */
@property (nonatomic, assign, readonly) uSDKDeviceWiFiFreqBrand wifiFreqBrand;


/**
 是否支持一键快连能力
 @since v8.8.0
 */
@property (nonatomic, assign, readonly) BOOL isSupportOneKeyConnect;

/// 快连埋点的trace对象
/// 自发现的deviceInfo和可控列表的device都会携带qcTrace对象,
/// 操作快连设备的埋点，需要使用qcTrace来进行点位添加操作，用来确保快连设备的traceID可以一直
/// @since v8.15.0
@property (nonatomic, strong, readonly) uTrace * qcTrace;

/// 标识快联设备是否需要确权
/// YES 表示快联设备进配置超过30分钟，需要走确权流程，NO 表示设备进配置不超过30分钟，可以进行普通配置绑定
/// @since 10.0.0
@property (nonatomic, assign, readonly) BOOL isNeedAuth;

/// 卡片页是否发现
/// YES 可以发现， NO 不能发现
/// @since 10.2.0
/// @since 10.6.0 标记废弃，需要APP自行根据逻辑来判断是否卡片页展示
@property (nonatomic, assign, readonly) BOOL isShowDialogs DEPRECATED_ATTRIBUTE;

/// 是否有工单
/// YES 有， NO 没有
/// @since 10.2.0
@property (nonatomic, assign, readonly) BOOL workOrder;

/// 设备上次绑定状态
///  0：设备未被绑定
///  1：请求用户为该设备上一次绑定所属家庭的创建者/管理员
///  2：请求用户不是该设备上一次绑定所属家庭的创建者/管理员
/// @since 10.6.0
@property (nonatomic, assign, readonly) NSInteger lastBindStatus;

/// 是否有增强模式
/// YES 有， NO 没有
/// @since 10.8.0
@property (nonatomic, assign, readonly) BOOL wifiEnhancedAbility;

/// 软件版本短id
/// 2个字符，用来标识设备的模组版本号, 可能为nil
/// @since 10.9.0
@property (nonatomic, copy, readonly) NSString * swShortId;

#pragma mark 自发现融合新增属性
/// productCode,wifiFreqBrand,configType,configState,controlState,apptypeName 为 原有属性

/// 自发现阶段存在的设备临时id，绑定成功之后，才会有真正的deviceId
/// @since 9.0.0
@property (nonatomic, copy ,readonly) NSString* deviceTempID;

/// 应用分类编码 例如 A030
/// @since 9.0.0
@property (nonatomic, copy ,readonly) NSString* appTypeCode;

/// 设备昵称，用于搜索发现阶段展示
/// 例如：滚筒洗衣机 C08C
/// @since 9.0.0
@property (nonatomic, copy ,readonly) NSString* nickname;

/// 设备url图标
/// @since 9.0.0
@property (nonatomic, copy ,readonly) NSString* deviceIcon;

/// App升级标识， 0 - 可直接配置绑定使用  1- 必须升级APP后才可以使用
/// @since 9.0.0
@property (nonatomic, assign ,readonly) int appUpgradeFlag;

/// 模组升级标识， 0 - 可直接配置绑定使用  1- 必须升级模组后才可以使用
/// @since 9.0.0
@property (nonatomic, assign ,readonly) int moduleUpgradeFlag;

/// 海极网定义的配置绑定方式
/// 例如： BLE&SoftAP
/// @since 9.0.0
@property (nonatomic, copy ,readonly) NSString* haigeekConfigMode;


#pragma mark 废弃属性
/**
 *  设备mac；
 @deprecated 5.4.0
 * update
 * @since 10.5.0 去掉废弃标记
 */
@property (nonatomic, copy ,readonly) NSString* mac;

/**
 设备是否可配置，用于BLE设备
 @deprecated 6.0.0
 */
@property (nonatomic, assign, readonly) BOOL isConfigurable DEPRECATED_ATTRIBUTE;

/**
 表示设备是否可被触发进配置
 @since 5.4.0
 @deprecated 6.0.0
 */
@property (nonatomic, assign, readonly) BOOL isTriggerConfigurable DEPRECATED_ATTRIBUTE;


/**
 *  APP通过解析获取到的远程家电设备json内容得到设备信息，生成远程家电设备对象。
 *
 *  @param deviceID                 设备ID
 *  @param uplusID                  设备类型唯一标识码
 *  @param isRemoteDeviceOnline     是否远程在线
 *
 *  @return 返回uSDKDeviceInfo实例
 */
-(instancetype)initWithDeviceID:(NSString*)deviceID
                        uplusID:(NSString*)uplusID
                       isOnline:(BOOL)isRemoteDeviceOnline;



/**
 APP通过解析获取到的远程家电设备JSON内容，生产远程家电设备。注：这个构造方式包含是否支持授权的字段信息。

 @param deviceID 设备ID
 @param uplusID 设备类型唯一标识码
 @param softwareType 设备是否支持授权,该字段也是从远程获取设备列表中的获得的
 @param isRemoteDeviceOnline 是否远程在线
 @return 返回uSDKDeviceInfo实例
 */
-(instancetype)initWithDeviceID:(NSString*)deviceID
                        uplusID:(NSString*)uplusID
                   softwareType:(NSString*)softwareType
                       isOnline:(BOOL)isRemoteDeviceOnline;


/**
 *  生成家电设备对象。
 *
 *  @param deviceID                 设备ID
 *  @param uplusID                  设备类型唯一标识码
 *
 *  @return 返回uSDKDeviceInfo实例
 */
-(instancetype)initWithDeviceID:(NSString*)deviceID
                        uplusID:(NSString*)uplusID;


/**
 生成家电设备对象

 @param deviceID 设备ID
 @param ssid 设备SSID
 @return 返回uSDKDeviceInfo实例
 */
- (instancetype)initWithDeviceID:(NSString*)deviceID
                            ssid:(NSString *)ssid;


/**
 生成家电设备对象 注：通过这个方法创建的设备对象 无法进行read、write、executeOperation、getBindInfo等操作，可以进行查询设备授权状态、取消授权的操作。
 
 @param deviceID 设备ID
 @return 返回uSDKDeviceInfo实例
 */
- (instancetype)initWithDeviceID:(NSString*)deviceID;



/// 扫描周围wifi列表
/// @param timeoutInterval 超时时间 5-30秒，默认10秒
/// @param success 成功回调
/// @param failure 失败回调
/// @sine 10.8.0
- (void)getWifiListWithTimeoutInterval:(NSTimeInterval)timeoutInterval
                               success:(void(^)(NSArray<uSDKAPInfo*>*wifiList))success
                               failure:(void(^)(NSError *error))failure;

@end

/**
 *  uSDKDeviceInfo对象代理协议，用于接收回调消息（设备状态变化、RSSI信号等级变化）
 */
@protocol uSDKDeviceInfoDelegate <NSObject>

@optional

/// 设备在RSSI信号等级状态变化
/// @param deviceInfo 状态变化的设备
/// @param BLESignalLevel 变化后的状态
/// @since 9.11.0
-(void)deviceInfo:(uSDKDeviceInfo*)deviceInfo didUpdateBLESignalLevel:(NSInteger)BLESignalLevel;

/// 设备在RSSI信号等级状态变化
/// @param deviceInfo 状态变化的设备
/// @param BLESignalLevel 变化后的状态
/// @since 9.11.0
-(void)deviceInfo:(uSDKDeviceInfo*)deviceInfo didUpdateQCBLESignalLevel:(NSInteger)QCBLESignalLevel;

@end

/**
 *  执行操作命令，特殊设备返回两种错误信息，对应此类中的两个成员变量。一般设备只返回一个错误码（只有一个属性有值）。
 Deprecated, 该类从uSDK5.2.0已废弃
 */
@interface uSDKErrorInfo : NSObject

/**
 *	@brief  功能描述：<br>
 *      错误信息号
 */
@property (nonatomic, assign) NSInteger errorNo;

/**
 *	@brief  功能描述：<br>
 *      错误信息id
 */
@property (nonatomic, assign) NSInteger errorInfoId;

@end


/// wifi信息
@interface uSDKAPInfo : NSObject

/// SSID
@property (nonatomic, copy) NSString *ssid;

/// BSSID
@property (nonatomic, copy) NSString *bssid;

/// 信号强度
@property (nonatomic, copy) NSString *rssi;

///wifi网络安全加密能力
@property (nonatomic, copy) NSString *capabilities;

/// 信道
@property (nonatomic, copy) NSString *channel;

@end
