//
//  uSDKConstantInfo.h
//  uSDK_iOS_v2
//
//  Created by <PERSON><PERSON> on 14-1-7.
//  Copyright (c) 2014年 haierubic. All rights reserved.
//


#ifndef uSDKConstantInfo_H
#define uSDKConstantInfo_H

// Include any system framework and library headers here that should be included in all compilation units.
// You will also need to set the Prefix Header build setting of one or more of your targets to reference this file.



//设备Mac及session均为NSString类型
#define DEVICE_LIST_CHANGED_NOTIFICATION     @"deviceListChangedNotify"	     //(NSArray*)发生变化的设备列表，元素为uSDKDevice对象
#define DEVICE_ONLINE_CHANGED_NOTIFICATION   @"deviceOnlineChangedNotify"	 //(NSDictionary*)发生变化的设备Mac:此设备的在线状态（uSDKDeviceStatusConst类型）
#define DEVICE_STATUS_CHANGED_NOTIFICATION   @"deviceStatusChangedNotify"	 //(NSDictionary*)发生变化的设备Mac:此设备当前发生的属性变化字典（NSDictionary类型）
#define DEVICE_ALARM_NOTIFICATION            @"deviceAlarmNotify"            //(NSDictionary*)发生变化的设备Mac:此设备当前上报的报警列表（NSArray类型）
#define DEVICE_INFRAREDINFO_NOTIFICATION     @"deviceInfraredInfoNotify"	 //(uSDKTransparentMessage*)当前上报的红外消息
#define BIGDATA_NOTIFICATION                 @"bigDataNotify"                //(uSDKTransparentMessage*)当前上报的大数据消息
#define DEVICE_BINDMESSAGE_NOTIFICATION      @"deviceBindMessageNotify"      //(NSDictionary*)远程session:设备mac
#define DEVICE_UNBINDMESSAGE_NOTIFICATION    @"deviceUnbindMessageNotify"    //(NSDictionary*)远程session:设备mac
#define BUSINESS_MESSAGE_NOTIFICATION        @"businessMessageNofify"        //(uSDKBusinessMessage*)当前推送的业务消息
#define SESSION_EXCEPTION_NOTIFICATION       @"sessionExceptionNotify"       //(NSString*)当前失效的远程session
#define DEVICE_OPERATION_ACK_NOTIFICATION    @"deviceOperationACKNotify"     //(NSDictionary*)命令sn:本次操作引起的属性变化字典

#define SUBDEVICE_LIST_CHANGED_NOTIFICATION  @"subDeviceListChangedNotify"   //(NSDictionary*)发生变化的复杂设备主机设备Mac:对应的发生变化的子机设备列表（NSDictionary类型）

#define INNER_ERROR_NOTIFICATION  @"innerErrorNotification"                         //上报server内部处理时产生的错误


/**
 *	设备大类分类
 */
typedef NS_ENUM(NSInteger,uSDKDeviceTypeConst)  {
    /**
     *  所有设备类型
     */
    ALL_TYPE = 0,
    /**
     *  冰箱
     */
    FRIDGE = 0x01,
    /**
     *  分体空调
     */
    SPLIT_AIRCONDITION = 0x02,
    /**
     *  柜机空调
     */
    PACKAGE_AIRCONDITION = 0x03,
    /**
     *  波轮洗衣机
     */
    PULSATOR_WASHING_MACHINE = 0x04,
    /**
     *  滚筒洗衣机
     */
    DRUM_WASHING_MACHINE = 0x05,
    /**
     *   电热水器
     */
    WATER_HEATER = 0x06,
    /**
     *  微波炉
     */
    MICRO_WAVE_OVEN = 0x07,
    /**
     *  酒柜
     */
    WINE_CABINET = 0x08,
    /**
     *  烟机
     */
    RANGE_HOOD = 0x09,
    /**
     *  洗碗机
     */
    DISH_WASHING_MACHINE = 0x0a,
    /**
     *  消毒柜
     */
    DISINFECTION_CABINET = 0x0b,
    /**
     *  保留
     *  @deprecated
     */
    RESERVE = 0x0c,
    /**
     *  商用空调
     */
    COMMERCIAL_AIRCONDITION = 0x0d,
    /**
     *  电视
     *  @deprecated 调整到0x0F
     */
    TV = 0x0e,
    /**
     *  影音娱乐
     */
    HOME_ENTERTAINMENT_EQUIPMENT = 0x0f,
    /**
     *  灯光照明
     */
    LIGHTING = 0x10,
    /**
     *  安防报警
     */
    SECURITY_EQUIPMENT = 0x11,
    /**
     *  视频监控
     */
    VIDEO_SURVEILLANCE = 0x12,
    /**
     *  传感器：
     *  @deprecated 调整所属大类，调整为0x12
     */
    SENSOR = 0x13,
    /**
     *  智能家居
     */
    SMART_HOME = 0x14,
    /**
     *   医疗健康
     */
    MEDICAL_CARE = 0x15,
    /**
     *  冷柜
     */
    REFRIDGERATOR = 0x16,
    /**
     *  医用柜
     */
    MEDICAL_CABINET = 0x17,
    /**
     *  燃气热水器
     */
    GAS_WATER_HEATER = 0x18,
    /**
     *  采暖炉
     */
    HEATING_FURNACE = 0x19,
    /**
     *  蒸箱
     */
    STEAM_BOX = 0x1a,
    /**
     *  咖啡机
     *  @deprecated 大类调整为 0x28
     */
    COFFEE_MAKER = 0x1b, // 咖啡机  表没有
    /**
     *  饮水机
     * @deprecated 大类调整为 0x28
     */
    WATER_MACHINE = 0x1c, // 饮水机   表没有
    /**
     *  灶具
     */
    COOKING = 0x1d,
    /**
     *  烤箱
     */
    OVEN = 0x1e,
    /**
     *  太阳能热水器
     */
    SOLAR_WATER_HEATER = 0x1f,
    /**
     *  热泵
     */
    HEAT_PUMP = 0x20,
    /**
     *  空气调节设备
     */
    AIR_CONDITIONING_DEVICE = 0x21,
    /**
     *  空气调节设备
     *  @deprecated 0x21(AIR_PURIFIER)-->调整到0x21(AIR_CONDITIONING_DEVICE)
     */
    AIR_PURIFIER = AIR_CONDITIONING_DEVICE,
    /**
     *  水处理设备
     */
    WATER_TREATMENT_DEVICE = 0x22,
    /**
     *  水处理设备
     *  @deprecated 0x22(WATER_PURIFIER)-->调整到0x22(WATER_TREATMENT_DEVICE)
     */
    WATER_PURIFIER = WATER_TREATMENT_DEVICE,
    /**
     *  水壶
     * @deprecated 大类调整为0x28
     */
    KETTLE = 0x23, // 表里没有
    /**
     *  新风设备
     */
    NEW_WIND_DEVICE = 0x24,
    /**
     *  采暖配套设备
     */
    FLOOR_HEATING_DEVICE = 0x25,
    /**
     *  公共服务类
     */
    PUBLIC_SERVICE = 0x26,
    /**
     *  吸尘器
     */
    DUST_PROOF = 0x27,
    /**
     *  小家电
     */
    KITCHEN_APPLIANCE = 0x28,
    /**
     *  环境监测设备
     */
    ENV_MONITOR = 0x29,
    /**
     *  衣柜鞋柜
     */
    CABINET = 0x2a,
    /**
     *  洗鞋机
     */
    SHOE_WASHER = 0x2b,
    /**
     *  网关
     */
    GATEWAY = 0x30,
    /**
     *  干衣机
     */
    DRYER = 0x31,
    /**
     *  可穿戴设备
     */
    WEARABLE_DEVICE = 0x32,
    /**
     *  空气魔方
     */
    AIR_CUBE = 0x33,
    /**
     *  浴室环境调节设备
     */
    AIR_HEATER = 0x34,
    /**
     *  其他
     */
    OTHER_DEVICE = 0x35,
    /**
     *  个人护理类
     */
    PERSONAL_CLEANER = 0x36,
    /**
     *  电风扇
     */
    ELECTRIC_FAN = 0x37,
    /**
     *  健身器材
     */
    FITNESS_EQUIPMENT = 0x38,
    /**
     *  窗体空调
     */
    WINDOW_AIR_CONDITIONER = 0x39,
    /**
     *  冰吧
     */
    ICE_BAR = 0x3a,
    /**
     *  智能音响
     */
    SMART_SPEAKER = 0x3b,
    /**
     *  厨宝
     */
    KITCHEN_WATER_HEATER = 0x3c,
    /**
     *  花洒
     */
    SHOWER = 0x3d,
    /**
     *  蒸烤一体设备
     */
    INTEGRATED_OVEN_AND_STEAM_BOX = 0x3e,
    /**
     *  集成灶
     */
    INTEGRATED_STOVE = 0x3f,
    /**
     *  机器人
     */
    ROBOT = 0xa1,
    /**
     *  路由模块
     */
    ROUTING_MODULE = 0xe1,
    /**
     *  路由器
     */
    SMART_ROUTER = 0xe2,
    /**
     *  控制终端
     */
    CONTROL_TERMINAL = 0xf1,
    
} ;


/**
 *	uSDK错误类型
 */
typedef  NS_ENUM(NSInteger,uSDKErrorConst) {
    /**
     *  uSDK接口执行成功
     */
    RET_USDK_OK = 0, // uSDK接口执行成功
    
    //通用0
    /**
     *  uSDK无效参数错误
     */
    ERR_USDK_INVALID_PARAM = -10001, // uSDK无效参数
    /**
     *  uSDK未启动
     */
    ERR_USDK_UNSTARTED = -10002,  //uSDK未启动
    /**
     *  uSDK接口超时
     */
    ERR_USDK_TIMEOUT = -10003, // uSDK接口超时
    /**
     *  内部错误
     */
    ERR_INTERNAL = -10006, // 内部错误
    /**
     *  uSDK已经停止
     */
    ERR_USDK_ALREADY_STOPPED = -10007, //uSDK已经停止
    /**
     *  云平台返回的错误信息，具体描述见NSError中的info
     */
    ERR_USDK_CLOUD_COMMON_ERROR = -10008, //云平台返回的错误信息，具体见NSError中的info
    /**
     *  云平台返回的错误信息，具体描述见NSError中的info
     */
    ERR_USDK_CAMERA_SDK_COMMON_ERROR = -10009, //CameraSDK返回的错误信息，具体描述见NSError中的info
    /**
     * HTTP请求返回的错误信息
    */
    ERR_USDK_HTTP_COMMON_ERROR = -10010,
    
    
    //通信1
    /**
     *  通过socket发送数据失败
     */
    ERR_USDK_SEND_DATA_TO_SERVER = -11001,
    
    //SDK管理2
    /**
     *  uSDK启动失败
     */
    ERR_USDK_START_FAILED = -12001, // uSDK启动失败
    /**
     *  uSDK正在启动
     */
    ERR_USDK_STARTING = -12002,   //uSDK正在启动
    /**
     *  路由器版本过低
     */
    ERR_USDK_ROUTER_VERSION_TOO_LOW = -12003,   //路由器版本过低
    /**
     *  路由器型号不支持
     */
    ERR_USDK_UNSPPORTED_ROUTER_TYPE = -12004,   //路由器型号不支持
    /**
     *  云连接离线
     */
    ERR_USDK_CLOUD_OFFLINE = -12005,
    /**
     *  不支持该特性
     */
    ERR_USDK_FEATURE_NOT_SUPPORT = -12006,

    
    //设备管理3
    /**
     *  远程设备重复
     */
    ERR_USDK_REMOTE_DEVICE_DUPLICATE = -13001,//远程设备重复
    /**
     *  远程设备不存在
     */
    ERR_USDK_REMOTE_DEVICE_NOT_EXIST = -13002,//远程设备不存在
    /**
     * 正在扫描设备
     */
    ERR_USDK_DEVICE_SCANNING = -13004,//正在扫描设备
    
    /**
     *   设备正在配置中
     */
    ERR_USDK_DEVICE_CONFIG_IN_PROGRESS=-13006,//设备正在配置中
    
    /**
     *   smartlink配置被取消
     */
    ERR_USDK_SMARTCONFIG_BE_CANCELED=-13007,//smartlink配置被取消
    
    /**
     *   配置命令发送完成,但未找到上线设备
     */
    ERR_USDK_RECV_ACK_BUT_NOT_FIND_DEVICE=-13008,
    
    /**
     *   softap配置完成,但未找到上线设备
     */
    ERR_USDK_RECV_SOFTAP_ACK_BUT_NOT_FIND_DEVICE=-13009,
    
    /**
     *   softap配置完成,但网络未切换到配置的SSID上
     */
    ERR_USDK_RECV_SOFTAP_ACK_BUT_NETWORK_NOT_SWITCH_TO_CONFIGED_SSID=-13010,
    
    /**
     *   无效的uplusID
     */
    ERR_USDK_DEVICE_UPLUSID_INVALID=-13011,
    /**
     *   首次执行GetConfigInfo未收到结果已超时
     */
    ERR_USDK_TIMEOUT_WITHOUT_GETCONFIGINFO_ACK=-13012,
    /**
     *   首次执行SoftApConfig未收到结果已超时
     */
    ERR_USDK_TIMEOUT_WITHOUT_SOFTAPCONFIG_ACK=-13013,
    /**
     *   softap配置被用户主动取消
     */
    ERR_USDK_SOFTAP_CONFIG_IS_CANCELED_BY_USER=-13014,
    /**
     *   uplusID不匹配
     */
    ERR_USDK_DEVICE_UPLUSID_NOT_MATCH=-13015,
    /**
     *   未连接到设备热点
     */
    ERR_USDK_CURRENT_SSID_IS_NOT_IOT=-13016,
    /**
     *   softap配置下发信息未收到ACK，但SSID已变化
     */
    ERR_USDK_SOFTAP_CONFIG_NOT_RECEIVE_ACK_BUT_SSID_CHANGED =-13017,
    /**
     *   softap配置未收到ACK, 且未找到上线设备
     */
    ERR_USDK_NOT_RECV_SOFTAP_ACK_AND_NOT_FIND_DEVICE=-13018,
    /**
     *   softap配置未收到ACK, 且网络未切换到配置的SSID上
     */
    ERR_USDK_NOT_RECV_SOFTAP_ACK_AND_NOT_SWITCH_TO_CONFIGED_SSID = -13019,
    /**
     *   密码长度不符合要求
     */
    ERR_USDK_PASSWORD_LENGTH_INVALID = -13020,
    //BLE
    /**
     *  蓝牙未打开
     */
    ERR_USDK_BLE_NOT_OPEN = -13021,
    /**
     *  未收到ACK
     */
    ERR_USDK_NOT_RECV_ACK = -13022,
    /**
     *  进入配对模式超时
     */
    ERR_USDK_ENTER_NETWORKING_MODE_TIMEOUT = -13023,
    /**
     *  获取绑定结果超时
     */
    ERR_USDK_GET_BIND_RESULT_TIMEOUT = -13024,
    /**
     *  等待组网结果超时
     */
    ERR_USDK_WAIT_NETWORKING_RESULT_TIMEOUT = -13025,
    /**
     *  App进入后台导致的超时
     */
    ERR_USDK_ENTER_BACKGROUND_TIMEOUT = -13026,
    /**
     *  设备需要触发进配置
     *  @deprecated 6.0.0
     */
    ERR_USDK_DEVICE_NEED_TRIGGER_CONFIG = -13027,
    /**
     *  设备不在配网状态
     */
    ERR_USDK_DEVICE_IS_NOT_CONFIG_STATE = -13028,
    /**
     *  设备校验失败
     */
    ERR_USDK_DEVICE_VERIFY_FAILED = -13029,
    /**
     *  设备校验超时
     */
    ERR_USDK_DEVICE_VERIFY_TIMEOUT = -13030,
    /**
     * 发送配置请求超时
     */
    ERR_USDK_SEND_CONFIG_REQ_TIMEOUT = -13031,
    
    /**
     * 不支持此设备 需要升级uSDK
     */
    ERR_USDK_UNSUPPORT_NEED_UPGRADE = -13032,
    
    /**
     * 已经启动了另一种自发现方式
     */
    ERR_USDK_OTHER_SCANNER_STARTING = -13033,
    /**
     * 不支持先绑后配功能
     */
    ERR_USDK_UNSUPPORT_WITHOUT_WIFI_BINDING = -13034,
    
    //设备4
    /**
     *   设备已连接（已订阅）
     */
    ERR_USDK_DEVICE_IS_CONNECTED = -14001,//设备已连接（已订阅）
    /**
     *   读取设备属性异常,respModel.attr.name is nil
     */
    ERR_USDK_DEVICE_READ_ATTR = -14002,//读取设备属性异常,respModel.attr.name is nil
    /**
     *  设备未连接（未订阅）
     */
    ERR_USDK_DEVICE_NOT_CONNECT = -14003,//设备未连接（未订阅）
    /**
     *  设备未连接成功或未就绪
     */
    ERR_USDK_DEVICE_NOT_CONNECTED = -14004,//设备未连接成功或未就绪
    /**
     *  无效的组命令名称
     */
    ERR_USDK_INVALID_GROUP_NAME = -14005,//无效的组命令名称
    /**
     *  子机无需连接
     */
    ERR_USDK_SUBDEVICE_DO_NOT_NEED_CONNECT = -14006,
    /**
     *  设备繁忙状态错误
     */
    ERR_USDK_DEVICE_IS_BUSY = -14007,
    
    /**
     *  不支持控制多个子机
     */
    ERR_USDK_NOT_SUPPORT_CONTROL_SUBDEVICE = -14008,
    
    /**
     *  设备不是本地设备(只有本地设备才能设置主网关域名)
     */
    ERR_USDK_DEVICE_NOT_LOCAL = -14009,//设备不是本地设备
    
    /**
     *  请先调用connectToCloud接口
     */
    ERR_USDK_CALL_CONNECT_TO_CLOUD_INTERFACE_FIRST = -14010,
    /**
     *  安全设备无token
     */
    ERR_USDK_SECURITY_DEVICE_WITHOUT_TOKEN = -14011,
    /**
     *  SERVER远程模块未初始化
     */
    ERR_USDK_SERVER_REMOTE_MODULE_NOT_INIT = -14012,
    /**
     *  SERVER本地模块未初始化
     */
    ERR_USDK_SERVER_LOCAL_MODULE_NOT_INIT = -14013,
    /**
     *  离线宣告导致离线
     */
    ERR_USDK_OFFLINE_DECLARE = -14014,
    /**
     *  子机消息上报超时，置为离线
     */
    ERR_USDK_SUBDEVICE_REPORT_MSG_TIMEOUT_SET_OFFLINE = -14015,
    /**
     *  设备离线
     */
    ERR_USDK_DEVICE_IS_OFFLINE = -14017,
    /**
     *  云平台已连接，请先断开云平台连接(disconnectToCloud)，再使用新token连接云平台(connectToCloud)
     */
    ERR_USDK_CLOUD_IS_CONNECTED = -14018,
  
    /**
     *  停止搜索导致的设备离线
     */
    ERR_USDK_STOP_SEARCH_CAUSE_OFFLINE = -14019,
    
    /**
     *  命令不支持
     */
    ERR_USDK_CMD_UNSUPPORTED = -14020,
    /**
     *  该设备没有网络信号质量指标
     */
    ERR_USDK_DEVICE_NOT_NETQUALITY = -14021,
    /**
     *  未获取到网络信号质量指标
     */
    ERR_USDK_DEVICE_NOT_GET_NETQUALITY = -14022,
    /**
     *  该设备不支持 授权
     */
    ERR_USDK_DEVICE_UNSUPPORTED_AUTHORIZE = -14023,
    /**
     *  设备授权失败
     */
    ERR_USDK_DEVICE_AUTHORIZE_FAILURE = -14024,
    /**
     *  设备取消授权失败
     */
    ERR_USDK_DEVICE_CANCEL_AUTHORIZE_FAILURE = -14025,
    /**
     *  查询设备授权状态失败
     */
    ERR_USDK_DEVICE_QUERY_AUTHORIZE_STATUS_FAILURE = -14026,
    /**
     *  该设备无效，无法进行相关操作
     */
    ERR_USDK_DEVICE_INVALID = -14027,
    /**
     *  找不到设备
     */
    ERR_USDK_DEVICE_NOT_FOUND = -14028,
    /**
     *  设备远程未就绪
     */
    ERR_USDK_DEVICE_REMOTE_STATE_NOT_READY = -14029,

    /**
     *  蓝牙的特性未找到
     */
    ERR_USDK_BLE_CHARACTERISTICS_NOT_FOUND = -14030,
    /**
     *  不支持的蓝牙操作
     */
    ERR_USDK_BLE_OPERATION_NOT_SUPPORT = -14031,
    /**
     *  网络开关被关闭
     */
    ERR_USDK_NETWORK_IS_NONE = -14032, //网络开关被关闭
    /**
     *  发生了切网
     */
    ERR_USDK_NETWORK_IS_SWITCHED = -14033, //发生了切网
    /**
     *  APP进入了后台
     */
    ERR_USDK_APP_ENTER_BACKGROUND = -14034, //APP进入了后台
    /**
     *  手机离线
     */
    ERR_USDK_PHONE_IS_OFFLINE = -14035,  //手机离线
    /**
     *  模式错误
     */
    ERR_USDK_MODE_ERROR_OFFLINE = -14036,  //模式错误
    
    /**
     *  蓝牙开关被关闭
     */
    ERR_USDK_BLE_IS_POWER_OFF = -14037,
    
    /**
     *  无升级信息，需要先调用checkBoardFOTAInfo接口获取升级信息
     */
    ERR_USDK_FOTA_INFO_IS_NULL = -14038,  //无升级信息，需要先调用checkBoardFOTAInfo接口获取升级信息
    /**
     *  设备（底板）无需升级
     */
    ERR_USDK_DONT_NEED_FOTA = -14039, //设备(底板)无需升级
    /**
     *  设备（模块）无需升级
     */
    ERR_USDK_DONT_NEED_OTA = -14040, //设备（模块）无需升级
    /**
     *  设备（模块）正在升级中
     */
    ERR_USDK_MODULE_IS_OTAING = -14041, //设备（模块）正在升级中
    /**
     *  设备（模块）OTA升级超时
     */
    ERR_USDK_MODULE_OTA_TIMEOUT = -14042, //设备（模块）OTA升级超时
    /**
     *  设备未被绑定
     */
    ERR_USDK_DEVICE_IS_NOT_BOUND = -14044,
    /**
     *  主机解订阅导致的离线
     */
    ERR_USDK_HOST_DEVICE_DISCONNECT_CAUSE_OFFLINE = -14045,    
    /**
     *  设备搜不到路由器或路由器密码错误导致离线，
     */
    ERR_USDK_DEVICE_NOT_CONNECT_ROUTER_CAUSE_OFFLINE = -14046,
    /**
     *  设备无需更新SSID PWD
     */
    ERR_USDK_DEVICE_DONT_NEED_UPDATE_SSID_PASSWORD = -14047,
    /**
     *  设备更新SSID PWD失败，SSID没搜到或PWD错误
     */
    ERR_USDK_DEVICE_UPDATE_SSID_PASSWORD_ERROR = -14048,
    /**
     *  设备更新SSID PWD失败，CAE直接返错
     */
    ERR_USDK_DEVICE_UPDATE_SSID_PASSWORD_CAE_ERROR = -14049,
    /**
     * bleDevId变化导致的BLE主动断开订阅
     */
    ERR_USDK_DEVICE_BLE_DISCONNECT_BY_BLE_DEVID_CHANGED = -14050,

    /**
     *  总状态合并导致的BLE主动断开订阅
     */
    ERR_USDK_DEVICE_BLE_DISCONNECT_BY_STATE_UPDATE = -14051,
    /**
     *  设备更新SSID PWD超时
    */
    ERR_USDK_UPDATE_SSID_PASSWORD_TIMEOUT = -14052,
    
    /**
     *  不是MESH设备
     */
    ERR_USDK_IS_NOT_MESH_DEVICE = -14053,
    /**
     *  不是组设备
     */
    ERR_USDK_IS_NOT_GROUP_DEVICE = -14054,
    
    //device group
    /**
     * 设备不具有分组能力
     */
    ERR_USDK_DEVICE_NOT_SUPPORT_GROUP = -14055,
    /**
     * 无可分组设备
     */
    ERR_USDK_NO_GROUPABLE_DEVICE = -14056,
    /**
     * 当前正在进行组操作
     */
    ERR_USDK_GROUP_IS_OPERATING = -14057,
    /**
     * 当前组设备不能创建新的组设备
     */
    ERR_USDK_GROUP_DEVICE_CAN_NOT_CREATE_GROUP = -14058,
    /**
     * 当前设备蓝牙通路不可达
     */
    ERR_USDK_DEVICE_BLE_IS_NOT_REACHABLE = -14059,
    /**
     * 接口不支持此类设备的OTA
     */
    ERR_USDK_NOT_SUPPORT_SUCH_DEVICE_OTA = -14060,
    /**
     * 当前设备主动与我们断开连接
     */
    ERR_USDK_DEVICE_DISCONNECT_FROM_US = -14061,
    /**
     * 设备（模块）OTA下载升级包失败
     */
    ERR_USDK_MODULE_DOWNLOAD_OTA_PKG_FAIL = -14062,
    /**
     * 当前设备鉴权未通过
     */
    ERR_USDK_DEVICE_IS_NOT_AUTHORIZED = -14063,


    
    /**
     * 设备正在唤醒中
     * @since: v8.10.0 新增设备正在唤醒中错误码
     */
    ERR_USDK_DEVICE_IS_WAKING_UP = -14064,
    /**
     * 设备正在休眠中或唤醒中
     * @since: v8.10.0 新增设备正在休眠或正在唤醒中错误码
     */
    ERR_USDK_DEVICE_IS_SLEEPING_OR_WAKING_UP = -14065,
    
    /**
     * 设备连接失败
     *
     * @since 8.16.0
     */
    ERR_USDK_DEVICE_CONNECT_FAIL = -14066,
    
    /**
     * 设备向云平台更新版本信息失败
     * @since v8.16.0
     */
    ERR_USDK_DEVICE_UPDATE_VERSION_FAIL = -14067,
    
    /**
     * 查询整机固件版本详细信息失败
     */
    ERR_USDK_QUERY_FW_DETAIL_INFO_FAIL = -14068,
    /**
     * 设备的FOTA channel 为空
     * @since v8.16.0
     */
    ERR_USDK_DEVICE_FOTA_CHANNEL_IS_NULL = -14069,
    
    
    /**
     * 设备正在被另外的手机升级
     *
     * @since 8.16.0
     */
    ERR_USDK_DEVICE_OTA_BY_OTHER_PHONE = -14070,
    
    /**
     * 设备正在进入产测模式
     *
     * @since 9.10.0
     */
    ERR_USDK_PROD_TEST_MODE_OPENING = -14074,
    /**
     * 设备当前不在产测模式
     *
     * @since 9.10.0
     */
    ERR_USDK_PROD_TEST_MODE_ILLEGAL = -14075,
    /**
     * 产测模式下不允许解订阅
     *
     * @since 9.10.0
     */
    ERR_USDK_PROD_TEST_MODE_CAN_NOT_DISCONN = -14076,

    
    //Trace
    /**
     *  链式跟踪未创建起点
     */
    ERR_USDK_TRACE_IS_NOT_STARTED = -15001,
    /**
     *  链式跟踪重复打点
     */
    ERR_USDK_TRACE_NODE_DUPLICATED = -15002,
    /**
     *  链式跟踪打点顺序错乱
     */
    ERR_USDK_TRACE_NODE_STEP_DISORDER = -15003,
    /**
     *  反射创建数据统计分析对象失败
     */
    ERR_USDK_TRACE_CREATE_UANALYTICS_FAILURE = -15004,
    
    //绑定流程
    /**
     *  设备正在绑定中
     */
    ERR_USDK_DEVICE_IS_IN_BINDING = -16001,
    /**
     *  设备不在绑定中
     */
    ERR_USDK_DEVICE_IS_NOT_IN_BINDING = -16002,
    /**
     *  设备绑定已取消
     */
    ERR_USDK_BIND_DEVICE_IS_CANCELED = -16003,
    /**
     *  uSDK获取设备bindInfo超时
     */
    ERR_USDK_GET_BINDINFO_TIMEOUT = -16004,
    /**
     *  绑定设备超时
     */
    ERR_USDK_BIND_DEVICE_TIMEOUT = -16005,
    /**
     *  安防模块未启动，请先调用uSDKManager类中的startSDKWithStartInfo接口
     */
    ERR_USDK_SECURITY_MODULE_NOT_STARTED = -16006,
    /**
     *  反射创建安防SDK绑定类失败。
     */
    ERR_USDK_CREATE_SECURITY_SDK_BIND_CLASS_FAIL = -16007,//反射创建安防SDK绑定类失败。
    /**
     *  设备mesh信息分配失败。
    */
    ERR_USDK_MESH_DEVICE_INFO_REQUEST_FAIL = -16008,//设备mesh信息分配失败。
   /**
    *  设备 provision 失败。
   */
   ERR_USDK_MESH_DEVICE_PROVISION_FAIL = -16009,//设备 provision 失败。
    
    /**
     *  TOKEN失效
     */
    ERR_USDK_USER_TOKEN_INVALID = -16010,
    /**
     *  用户绑定数量达到上限
     */
    ERR_USDK_USER_BIND_COUNT_LIMIT = -16011,
    /**
     *  小循环搜到且mqtt或https消息超时
     */
    DEVICE_LOCAL_EXIST_AND_GET_BIND_RESULT_TIMEOUT = -16012,
    /**
     *  小循环没搜到且mqtt或https消息超时
     */
    DEVICE_LOCAL_NOT_EXIST_AND_GET_BIND_RESULT_TIMEOUT = -16013,
    /**
     *  mqtt或https成功，但无此次绑定的绑定结果
     */
    ERR_USDK_NO_RESULT_BINDING = -16014,
    /**
     *  mqtt或https消息超时
     */
    PHONE_BIND_DEVICE_TIMEOUT = -16015,
    /**
     *  开启绑定时间窗超时
    */
    ERR_USDK_SECURITY_BINDING_QR_CODE_TIMEOUT = -16016,
    /**
     *  安防侧绑定超时
    */
    ERR_USDKSECURITY_DEVICE_BIND_TIMEOUT = -16017,
    /**
     *  绑定超时需要重试绑定
    */
    ERR_USDK_BIND_TIMEOUT_NEED_RETRY_BIND = -16018,
    /**
     *  没有需要重试的绑定
    */
    ERR_USDK_NO_NEED_RETYR_BIND = -16019,
    /**
     *  设备 configuration 失败。
    */
    ERR_USDK_MESH_DEVICE_CONFIGURATION_FAIL = -16020,//设备 configuration 失败。
    /**
     *  不支持获取配置的路由器信息的操作。
     */
    ERR_USDK_GET_CONFIG_ROUTERINFO_OPERATION_NOT_SUPPORT = -16021,
    /**
     *  获取配置的路由器信息失败。
    */
    ERR_USDK_GET_CONFIG_ROUTERINFO_FAIL = -16022,
    /**
     *  获取配置的路由器信息超时。
    */
    ERR_USDK_GET_CONFIG_ROUTERINFO_TIMEOUT = -16023,
    /**
     *  保存设备版本信息失败。
    */
    ERR_USDK_SAVE_DEVICE_VERSION_FAIL = -16024,
    
    /**
     扫码授权绑定流程中，二维码已失效
     
     @since 9.5.0
     */
    ERR_USDK_QRAUTH_QRCODE_INVALID = -16027,
    
    /**
     *  设备不支持一键快连
     */
    ERR_USDK_NOT_SUPPORT_ONE_KEY_CONNECT = -16028,
    
    /**
     *  查询登记结果超时
     */
    ERR_USDK_GET_REGISTER_RESULT_TIMEOUT = -16029,
    /**
     *  不支持ToB的方法
     */
    ERR_TOB_NOT_SUPPORT_ERROR = -16032,
    /**
     *  不支持批量绑定的设备
     */
    ERR_BATCH_BIND_NOT_SUPPORT_ERROR = -16033,
    /*
     * 二维码解析失败，纳辉项目添加
     */
    ERR_QRCODE_DECODE_FAILURE = -16034,
    
    /**
     *  mesh网络未准备好，或不存在mesh网络
     */
    ERR_USDK_BLE_MESH_NOT_READY = -16500,
    /**
     *  设备blemesh通路未连接成功或就绪
     */
    ERR_USDK_DEVICE_BLE_MESH_CHANNLE_NOT_READY = -16501,
    /**
     *  BLEMesh通用错误，具体描述见NSError中的info
     */
    ERR_USDK_BLE_MESH_COMMON_ERROR = -16502,

    
    
    
    
    
    
    //ping
    /**
     *  ping正在进行中
     */
    ERR_USDK_PING_IS_IN_WORKING = -17001,
    /**
     *  ping被中断
     */
    ERR_USDK_PING_IS_STOPED = -17002,
    
    //HTTPDNS域名解析
    /**
     *  域名解析错误，具体描述见NSError中的info字段
     */
    ERR_USDK_HTTPDNS_ERROR = -18001,
    
    /**
     *  ping网关失败
     */
    ERR_USDK_NETWORK_IS_DOWN = -19001,
    /**
     *  手机未搜索到设备，无法进FOTA相关操作
     */
    ERR_USDK_NOT_SEARCH_DEVICE_CAUSE_NOT_OTA = -19010,
    /**
     *  OTA升级包下载失败
     */
    ERR_USDK_DEVICE_OTA_PKG_DOWNLOAD_FAIL = -19011,
    /**
     *  OTA升级包校验失败
     */
    ERR_USDK_DEVICE_OTA_PKG_VALIDATE_FAIL = -19012,
    /**
     *  模块不处于ota状态中
     */
    ERR_USDK_DEVICE_NOT_IN_OTA_MODE = -19013,
    /**
     *  设备升级异常终止
     */
    ERR_USDK_DEVICE_OTA_INTERRUPTED = -19014,
    /**
     *  设备OTA升级超时
     */
    ERR_USDK_DEVICE_SEND_OTA_FILE_TIMEOUT = -19015,    
} ;

/**
 *	wifi热点的加密方式
 */
typedef  NS_ENUM(NSInteger,uSDKApEncryptionTypeConst) {
    /**
     *  未知
     */
    AP_ENCRYPTION_UNKNOWN = 0,
    /**
     *  无加密
     */
    AP_ENCRYPTION_NONE,
    /**
     *  WEP加密
     */
    AP_ENCRYPTION_WEP,
    /**
     *  WPA/WPA2加密
     */
    AP_ENCRYPTION_WPA_WPA2,
} ;

/**
 *	云连接状态
 */
typedef NS_ENUM(NSInteger,uSDKCloudConnectionState) {
    /**
     *  未连接
     */
    uSDKCloudConnectionStateUnconnect = 0,
    /**
     *  连接中
     */
    uSDKCloudConnectionStateConnecting,
    /**
     *  已连接
     */
    uSDKCloudConnectionStateConnected,
    /**
     *  连接失败
     */
    uSDKCloudConnectionStateConnectFailed
};


typedef NS_OPTIONS(NSUInteger, uSDKLogFlag){
    
    USDK_LEVEL_ERROR        = (1 << 0), //1
    
    USDK_LEVEL_WARNING      = (1 << 1), //2
   
    USDK_LEVEL_INFO         = (1 << 2), //4
    
    USDK_LEVEL_DEBUG        = (1 << 3), //8
    
};
/**
*    日志级别
*/
typedef NS_ENUM(NSUInteger, uSDKLogLevelConst){
    /**
    *  无
    */
    USDK_LOG_NONE       = 0,
    /**
    *  error
    */
    USDK_LOG_ERROR     = (USDK_LEVEL_ERROR),
    /**
    *  warning
    */
    USDK_LOG_WARNING   = (USDK_LOG_ERROR   | USDK_LEVEL_WARNING),
    /**
    *  info
    */
    USDK_LOG_INFO      = (USDK_LOG_WARNING | USDK_LEVEL_INFO),
    /**
    *  debug
    */
    USDK_LOG_DEBUG     = (USDK_LOG_INFO    | USDK_LEVEL_DEBUG)    
};


/**
 *  uSDK启动状态标识
 */
typedef NS_ENUM(NSInteger, uSDKState){
    /**
     *  uSDK未启动
     */
    uSDKStateUnstart,
    /**
     *  uSDK启动中
     */
    uSDKStateStarting,
    /**
     *  uSDK已启动
     */
    uSDKStateStarted
};

/**
 *	特性定义
 */
typedef NS_OPTIONS(NSUInteger, uSDKFeatures) {
    /**
     *	不开启任何特性
     */
    uSDKFeatureNone = 0,
    /**
     *	默认，开启全部特性
     */
    uSDKFeatureDefault = NSUIntegerMax,
    /**
     *  链式跟踪特性
     *
     * @deprecated 5.4.1
     * @warning 该特性已不允许设置，国内版本将自动启动链式跟踪功能，国外版本没有链式跟踪功能
     */
    uSDKFeatureTrace = (1UL << 0),
    /**
     *	DNS解析特性
     * @deprecated 5.4.1
     * @warning 该功能uSDK已删除
     */
    uSDKFeatureDNSParse = (1UL << 1),
    /**
     *    HTTPDNS解析特性
     */
    uSDKFeatureHTTPDNSParse = (1UL << 2),
    /**
     *  网络环境
     * @deprecated 5.4.1
     * @warning 该特性已不允许设置，国内版本将自动启动链式跟踪功能，国外版本没有链式跟踪功能
     */
    uSDKFeatureNetEnv = (1UL << 3),
    /**
     *  本地DNS解析特性
     * @deprecated 9.7.1
     * @warning 该特性默认开启，会进行本地NDS解析。  若需要关闭，请主动进行设置。
     */
    uSDKFeatureLocalDNSParse = (1UL << 4),

} ;

/**
 *	设备信号强度
 */
typedef NS_ENUM(NSUInteger, DeviceNetQuality) {
    /**
     *	设备信号强度差
     */
    DeviceNetQualityWeak,
    /**
     *	设备信号强度中
     */
    DeviceNetQualityMiddle,
    /**
     *	设备信号强度良
     */
    DeviceNetQualityGood,
    /**
     *	设备信号强度优
     */
    DeviceNetQualityStrong,
};

/**
*    设备更新SSID Password的进度通知
*/
typedef NS_ENUM(NSUInteger, uSDKRouterInfoUpdateProgress) {
    /**
     连接中
     */
    uSDKRouterInfoUpdateProgressConnecting,
    /**
     更新中
     */
    uSDKRouterInfoUpdateProgressUpdating
};

/**
 uSDK的数据中心
*/
typedef NS_ENUM(NSUInteger, uSDKIDCArea) {
    /**
     国内数据中心
     */
    uSDKIDCAreaChina,
    /**
     美国数据中心
     */
    uSDKIDCAreaAmerica,
    /**
     欧洲数据中心
     */
    uSDKIDCAreaEurope,
    /**
     东南亚数据中心
     */
    uSDKIDCAreaSoutheastAsia,
    /**
     自定义数据中心
     */
    uSDKIDCAreaCustom,
};

#endif /* uSDKConstantInfo_H */
