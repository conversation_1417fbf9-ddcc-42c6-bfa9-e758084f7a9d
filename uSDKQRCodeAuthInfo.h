//
//  uSDKQRCodeAuthInfo.h
//  uSDK
//
//  Created by 夏明伟 on 2022/12/7.
//  Copyright © 2022 haier. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface uSDKQRCodeAuthInfo : NSObject

/**
 二维码内容
 */
@property (nonatomic, copy) NSString *QRCodeUrl;

/**
 超时时间
 1:  获取授权类型流程中，默认超时时间是：60s
 2: 授权绑定流程中，默认超时时间是：120s
 */
@property (nonatomic, assign) NSTimeInterval timeout;

/**
 链式跟踪节点
 */
@property (nonatomic, strong, nullable) uTraceNode *traceNodeCS;


@end

NS_ASSUME_NONNULL_END
