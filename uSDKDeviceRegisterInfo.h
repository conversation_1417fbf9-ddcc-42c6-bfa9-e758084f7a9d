//
//  uSDKDeviceRegisterInfo.h
//  uSDK
//
//  Created by oet on 2022/3/11.
//  Copyright © 2022 haier. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

/// 设备登记、取消登记通知信息类
@interface uSDKDeviceRegisterInfo : NSObject

///设备的deviceID
@property (nonatomic, copy) NSString *devId;

///空间ID；
@property (nonatomic, copy) NSString* spId;

///设备最近一次登记、取消登记的时间戳，单位：毫秒
@property (nonatomic, assign) NSTimeInterval ts;

///错误码。如登记成功，则值为0；如登记失败： 1：设备未授权；
@property (nonatomic, assign) NSInteger errNo;



@end

NS_ASSUME_NONNULL_END
