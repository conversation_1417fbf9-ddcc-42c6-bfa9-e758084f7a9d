//
//  uSDKSystemCapabilityManager.h
//  uSDK
//
//  Created by like on 2024/1/4.
//  Copyright © 2024 haier. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN


/**
 *  uSDK蓝牙扫描状态
 */
typedef NS_ENUM(NSInteger, uSDKBLEScanState){
    /**
     *  停止
     */
    uSDKBLEScanStateStopped,
    /**
     *  启动
     */
    uSDKBLEScanStateStarted,
    /**
     *  暂停
     */
    uSDKBLEScanStatePause
};

/**
 *  uSDK系统能力管理类。
 */
@interface uSDKSystemCapabilityManager : NSObject

/**
 *  蓝牙扫描状态。
 */
@property (nonatomic, assign) uSDKBLEScanState bleScannerState;

/**
 *  单例，获取管理类。
 */
+ (instancetype)sharedManager;

/**
 *  蓝牙扫描暂停。
 */
- (void)bleScannPause;

/**
 *  蓝牙扫描恢复。
 */
- (void)bleScanResume;

@end

NS_ASSUME_NONNULL_END
