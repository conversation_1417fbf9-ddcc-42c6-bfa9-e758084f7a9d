//
//  uSDKBinding.h
//  uSDK
//
//  Created by wangbing on 2018/7/6.
//  Copyright © 2018年 haier. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "uSDKDeviceInfo.h"
#import "uSDKBLEBindInfo.h"
#import "uSDKDevice.h"
#import "uSDKSoftApBindInfo.h"
#import "uSDKQRCodeBindInfo.h"
#import "uSDKSmartLinkBindInfo.h"
#import "uSDKBindProgressInfo.h"
#import "uSDKPureBLEBindInfo.h"
#import "uSDKCameraScanQRCodeBindInfo.h"
#import "uSDKSlaveDeviceBindInfo.h"
#import "uSDKBLEMeshBindInfo.h"
#import "uSDKNewDirectLinkManualConfirmBindInfo.h"
#import "uSDKNewDirectLinkVerificationCodeBindInfo.h"
#import "uSDKNewDirectLinkWithoutVerificationBindInfo.h"
#import "uSDKBLEADVBindInfo.h"
#import "uSDKConfigRouterInfo.h"
#import "uSDKWithoutWifiBindInfo.h"
#import "uSDKOneKeyConnectBindInfo.h"
#import "uSDKCellularDeviceBindInfo.h"
#import "uSDKDeviceWithError.h"
#import "uSDKBLEMeshBatchBindInfo.h"
#import "uSDKQRAuthCodeInfo.h"
#import "uSDKQRCodeAuthInfo.h"
#import "uSDKBatchConfigBindInfo.h"
#import "uSDKOpenWindowDeviceInfo.h"
/**
 设备配置绑定类
 */
@interface uSDKBinding : NSObject


/**
 BLE配网接口，将设备接入指定的WiFi，并将设备连接到云平台
 调用该接口前，需要成功调用uSDKDeviceManager中的connectToCloud接口

 @param device 通过uSDKDeviceScanner扫描到的蓝牙设备对象，device.configType & uSDKDeviceConfigTypeBLE == 1表示该设备支持蓝牙配网
 @param bindInfo 配网绑定信息
 @param progressNotify 状态上报信息：(三个进度)
 1.开始连接BLE设备时上报uSDKBindProgressConnectDevice
 2.开始向设备发送配置信息时上报uSDKBindProgressSendConfigInfo
 3.配置信息发送成功时上报uSDKBindProgressBindDevice
 @param success 配网成功时的block回调
 @param failure 配网失败时的block回调
 
 设备正在配置中。ERR_USDK_DEVICE_CONFIG_IN_PROGRESS=-13006,
 蓝牙开关被关闭。ERR_USDK_BLE_IS_POWER_OFF = -14037,
 //蓝牙配置错误段: -931 ~ -949
 #define CAE_BLE_CFG_ERR_SEARCH_FAIL     (-931)
 #define CAE_BLE_CFG_ERR_CNT_FAIL        (-932)
 #define CAE_BLE_CFG_ERR_PROFILE         (-933)
 #define CAE_BLE_CFG_ERR_WRITE_FAIL      (-934)
 #define CAE_BLE_CFG_ERR_READ_FAIL       (-935)
 #define CAE_BLE_CFG_ERR_DATA            (-936)
 #define CAE_BLE_CFG_ERR_CANCELED        (-937)
 #define CAE_BLE_CFG_ERR_AUTH            (-938)
 #define CAE_BLE_CFG_ERR_CFG_FAIL        (-939)
 #define CAE_BLE_CFG_ERR_REJECT          (-940)
 #define CAE_BLE_CFG_ERR_DOING           (-950)
 发送配置请求超时。ERR_USDK_SEND_CONFIG_REQ_TIMEOUT = -13031,
 小循环搜到且mqtt或https消息超时。DEVICE_LOCAL_EXIST_AND_GET_BIND_RESULT_TIMEOUT = -16012,
 小循环没搜到且mqtt或https消息超时。DEVICE_LOCAL_NOT_EXIST_AND_GET_BIND_RESULT_TIMEOUT = -16013,
 绑定超时需要重试绑定。ERR_USDK_BIND_TIMEOUT_NEED_RETRY_BIND = -16018,
 */
+ (void)bindDeviceByBLE:(uSDKDeviceInfo *)device
               bindInfo:(uSDKBLEBindInfo *)bindInfo
         progressNotify:(void(^)(uSDKBindProgressInfo *bindProgressInfo))progressNotify
                success:(void(^)(uSDKDevice *device))success
                failure:(void(^)(NSError *error))failure;


/**
 SoftAp配网接口，将设备接入指定的WiFi，并将设备连接到云平台
 调用该接口前，需要成功调用uSDKDeviceManager中的connectToCloud接口

 @param softApBindInfo 配网绑定信息，注意uSDKSoftApBindInfo.bssid字段要求为必填
 @param progressNotify 状态上报信息
 1.开始从设备获取配置信息时上报uSDKBindProgressConnectDevice
 2.成功获取设备信息，开始向设备发送配置信息时上报uSDKBindProgressSendConfigInfo
 3.配置信息发送成功时上报uSDKBindProgressBindDevice
 @param switchNetworkNotify 切网消息，如果设备支持设备自绑定，则没有该状态回调；如果是手机绑定，则在成功发送配置信息后回调该消息，提示切网
 @param success 配网成功时的block回调
 @param failure 配网失败时的block回调
 
 设备正在配置中。ERR_USDK_DEVICE_CONFIG_IN_PROGRESS=-13006,
 未连接到设备热点。ERR_USDK_CURRENT_SSID_IS_NOT_IOT = -13016,
 GetConfigInfo未收到结果已超时。 ERR_USDK_TIMEOUT_WITHOUT_GETCONFIGINFO_ACK  = -13012

 路由器断电等导致找不到路由器。USDK_ADV_STATE_CODE_CFGING_NO_ROUTER = 2001,
 用户修改路由信息导致无法连上路由器。USDK_ADV_STATE_CODE_CFGING_WRONG_PWD = 2002,
 疑似密码错误。USDK_ADV_STATE_CODE_CFGING_SUSPECT_WRONG_PWD = 2003

 发送配置请求超时。ERR_USDK_SEND_CONFIG_REQ_TIMEOUT = -13031,
 小循环搜到且mqtt或https消息超时。DEVICE_LOCAL_EXIST_AND_GET_BIND_RESULT_TIMEOUT = -16012,
 小循环没搜到且mqtt或https消息超时。DEVICE_LOCAL_NOT_EXIST_AND_GET_BIND_RESULT_TIMEOUT = -16013,

 softap配置未收到ACK, 且未找到上线设备。ERR_USDK_NOT_RECV_SOFTAP_ACK_AND_NOT_FIND_DEVICE = -13018
 softap配置完成,但未找到上线设备。ERR_USDK_RECV_SOFTAP_ACK_BUT_NOT_FIND_DEVICE = -13009
 softap配置未收到ACK, 且网络未切换到配置的SSID上。ERR_USDK_NOT_RECV_SOFTAP_ACK_AND_NOT_SWITCH_TO_CONFIGED_SSID = -13019
 softap配置完成,但网络未切换到配置的SSID上。ERR_USDK_RECV_SOFTAP_ACK_BUT_NETWORK_NOT_SWITCH_TO_CONFIGED_SSID = -13010

 绑定超时需要重试绑定。ERR_USDK_BIND_TIMEOUT_NEED_RETRY_BIND = -16018,
 */
+ (void)bindDeviceBySoftAp:(uSDKSoftApBindInfo *)softApBindInfo
            progressNotify:(void(^)(uSDKBindProgressInfo *bindProgressInfo))progressNotify
       switchNetworkNotify:(void(^)(void))switchNetworkNotify
                   success:(void(^)(uSDKDevice *device))success
                   failure:(void(^)(NSError *error))failure;


/**
 二维码扫描绑定
 调用该接口前，需要成功调用uSDKDeviceManager中的connectToCloud接口

 @param qrCodeBindInfo 二维码绑定信息
 @param success 配网成功时的block回调
 @param failure 配网失败时的block回调
 */
+ (void)bindDeviceByQRCode:(uSDKQRCodeBindInfo *)qrCodeBindInfo
                   success:(void(^)(uSDKDevice *device))success
                   failure:(void(^)(NSError *error))failure;

#pragma mark - wifi 批绑

/// wifi 批量绑定接口
/// - Parameters:
///   - bindInfo: 批量绑定的信息对象
///   - progressNotify: 绑定进度回调，包括进度和当前进度的设备id
///   - boundNotify: 绑定结果通知，包括入参的绑定的设备id和完成的设备对象，如果失败，则赋值error值
///   - completion: 完成回调。
+ (void)batchConfigBindDeviceWithBindInfo:(uSDKBatchConfigBindInfo *)bindInfo
                           progressNotify:(void(^)(uSDKBindProgressInfo *bindProgressInfo, NSString * configDevId))progressNotify
                              boundNotify:(void(^)(NSString * configDevId,uSDKDevice *device, NSError * error))boundNotify
                               completion:(void(^)(NSError *error))completion;


/// 取消正在进行的wifi批量绑定
/// - Parameters:
///   - success: 取消成功
///   - failure: 取消失败
+ (void)cancelBatchConfigBindDeviceSuccess:(void(^)(void))success
                                   failure:(void(^)(NSError * _Nonnull error))failure;

#pragma mark - 云直连绑定
/// 获取开窗设备
/// - Parameters:
///   - moduleID: 设备模块id，可以输入imei码或wifi_mac
///   - timeout: 超时时间 范围 【5-120】，默认30s（timeout传0 则使用默认值）
///   - success: 成功回调，返回数据为设备ID和模块类型
///   - failure: 失败回调
+ (void)getOpenWindowsDevices:(NSString *)moduleID
                      timeout:(NSInteger)timeout
                      success:(void(^)(NSArray<uSDKOpenWindowDeviceInfo *>* openDevInfos))success
                      failure:(void(^)(NSError * _Nonnull error))failure;


/// 绑定已开窗设备
/// - Parameters:
///   - deviceInfo: 获取开窗设备返回的模型对象
///   - timeout: 超时时间  范围 [15, 120]，默认30s（timeout传0 则使用默认值）
///   - success: 成功回调
///   - failure: 失败回调
+ (void)bindOpenWindowDevice:(uSDKOpenWindowDeviceInfo *)deviceInfo
                     timeout:(NSInteger)timeout
                     success:(void(^)(uSDKDevice *device))success
                     failure:(void(^)(NSError *error))failure;

#pragma mark - 扫码授权绑定

/**
 
 获取二维码授权类型
 
 @param qrCodeAuthInfo 二维码授权绑定信息, 默认超时时间是60s
 @param success 获取授权类型成功的回调
 @param failure 获取授权类型失败的回调
 
 @since 9.6.0
 */
+ (void)getQRAuthType:(uSDKQRCodeAuthInfo *_Nonnull)qrCodeAuthInfo
              success:(nullable void(^)(uSDKQRAuthCodeInfo *_Nonnull qrAuthCodeInfo))success
              failure:(nullable void(^)(NSError *_Nonnull error))failure;

/**
 
 二维码授权绑定
 
 @param qrCodeAuthInfo 二维码授权绑定信息, 默认超时时间是120s
 
 成功回调中，uSDKDevice可为空：
 仅授权流程中，授权完成后，返回成功，并且uSDKDevice 对象为空。
 仅绑定或者授权且绑定流程中，绑定完成后，返回成功，uSDKDevice 对象不为空。
 
 @param success 授权绑定成功的回调
 @param failure 授权绑定失败的回调
 
 @since 9.6.0
 */
+ (void)bindDeviceByQRAuth:(uSDKQRCodeAuthInfo *_Nonnull)qrCodeAuthInfo
                   success:(nullable void(^)(uSDKDevice *_Nullable device))success
                   failure:(nullable void(^)(NSError *_Nullable error))failure;

/**
 取消授权登录
 
 @brief 向用户中心发起取消授权登录请求。
 
 @param qrCodeAuthInfo 取消二维码授权登录信息, 默认超时时间是10s
 @param success 取消授权登录成功的回调
 @param failure 取消授权登录失败的回调，错误码及错误描述信息均为透传。
 
 @since 9.6.0
 */
+ (void)cancelBindDeviceByQRAuth:(uSDKQRCodeAuthInfo *_Nonnull)qrCodeAuthInfo
                         success:(nullable void(^)(void))success
                         failure:(nullable void(^)(NSError *_Nullable error))failure;

/**
 SmartLink配置绑定接口，将指定的设备接入指定的WiFi，并将设备绑定到云平台
 调用该接口前，需要成功调用uSDKDeviceManager中的connectToCloud接口
 
 @param smartLinkBindInfo 配置信息
 @param progressNotify 进度回调
    1.开始发送配置信息时上报uSDKBindProgressSendConfigInfo
    2.开始获取绑定信息时上报uSDKBindProgressBindDevice，并携带uplusID和DeviceID
 @param success 配置绑定成功时的block回调，只有配置和绑定都成功会通过success block进行回调
 @param failure 配置绑定失败时的block回调，任何一个过程失败都会通过failure block进行回调
 */
+ (void)bindDeviceBySmartLink:(uSDKSmartLinkBindInfo *)smartLinkBindInfo
               progressNotify:(void(^)(uSDKBindProgressInfo *bindProgressInfo))progressNotify
                      success:(void(^)(uSDKDevice *device))success
                      failure:(void(^)(NSError *error))failure;


/**
 纯蓝牙设备配置绑定接口，根据uSDKDeviceInfo中的configType属性，判断是否为纯蓝牙设备，如果是，则可以通过该接口进行配置绑定
 configType & uSDKDeviceConfigTypePureBLE == 1，表示纯蓝牙设备
 @param bindInfo 配置信息
 @param progressNotify 进度回调，共三个进度回调：
    1. uSDKBindProgressConnectDevice
    2. uSDKBindProgressSendConfigInfo
    3. uSDKBindProgressBindDevice
 @param success 配网成功时的block回调
 @param failure 配网失败时的block回调
 
蓝牙开关被关闭。ERR_USDK_BLE_IS_POWER_OFF = -14037,
发送配置请求超时。ERR_USDK_SEND_CONFIG_REQ_TIMEOUT = -13031,
蓝牙配网过程中 可能出现的错误码：
   @(60001):@"连接路由器失败，其他错误",
   @(60002):@"连接路由器失败，密码错误",
   @(60003):@"连接路由器失败，找不到SSID",
   @(60004):@"连接路由器失败，信号弱",
   @(60005):@"连接路由器失败，未获取到IP",
   @(60006):@"配置超时，未接收到WIFI SSID和密码",
   @(60007):@"正在连接路由器",
   @(60008):@"疑似密码错误",
   @(60010):@"首次连接主网关，连接中",
   @(60011):@"首次连接主网关失败，DNS失败",
   @(60012):@"首次连接主网关失败，TCP连接失败",
   @(60013):@"首次连接主网关失败，TLS连接失败",
   @(60014):@"首次连接主网关失败，未收到主网关上报设备版本应答",
   @(60020):@"首次连接网关，连接中",
   @(60021):@"连接网关失败，DNS失败",
   @(60022):@"连接网关失败，TCP连接失败",
   @(60023):@"连接网关失败，TLS连接失败",
   @(60024):@"连接网关失败，USS握手失败",
   @(60025):@"连接网关失败，未收到上报设备版本应答",
   @(60026):@"未收到开启绑定时间窗应答",
   @(60027):@"未收到获取bindkey应答",
   @(60030):@"绑定错误-输出参数不能为空",
   @(60031):@"绑定错误-用户会话状态不存在",
   @(60032):@"绑定错误-已超过绑定数量",
 
 绑定超时需要重试绑定。ERR_USDK_BIND_TIMEOUT_NEED_RETRY_BIND = -16018,

 
 @since 5.4.0
 @since 8.2.0 新增蓝牙门锁配置方式
 */
+ (void)bindPureBLEDevice:(uSDKPureBLEBindInfo *)bindInfo
           progressNotify:(void(^)(uSDKBindProgressInfo *bindProgressInfo))progressNotify
                  success:(void(^)(uSDKDevice *device))success
                  failure:(void(^)(NSError *error))failure;


/**
 安防摄像头配置绑定接口

失败的回调中 NSError对象的 code字段 可取值为：
        ERR_USDK_UNSTARTED，
        ERR_USDK_CALL_CONNECT_TO_CLOUD_INTERFACE_FIRST，
        ERR_USDK_INVALID_PARAM，
        ERR_USDK_TIMEOUT，
        ERR_USDK_CREATE_SECURITY_SDK_BIND_CLASS_FAIL，
        ERR_USDK_CAMERA_SDK_COMMON_ERROR，
 
 注意：当code == ERR_USDK_CAMERA_SDK_COMMON_ERROR 时，具体的错误信息，在userInfo中， NSLocalizedFailureReasonErrorKey 和 NSLocalizedDescriptionKey 通过这两个key可取到如下
 
 "A00001", "系统异常"
 "A00002", "操作失败"
 "032107", "摄像头未扫码成功,无法重试绑定!"
 "030240", "设备绑定失败--设备类型不支持!"
 "030239", "IoT平台设备绑定失败!"
 "030238", "云平台设备注册失败"
 "030205", "绑定失败--云平台设备绑定失败"
 "030201", "绑定失败"
 
 "H_000000", "绑定成功"
 "H_000001", "SDK初始化失败 需要重新启动SDK"
 "H_000002", "传参出错 ssid password typeId 为空 或者 typeid在配置表中不存在"
 "H_000003", "绑定设备超时"
 "H_000004", "设备已经被绑定"
 "H_000005", "生成二维码失败"
 "H_000006", "设备未注册,无法绑定(摄像头专有 门铃没有此错误码)"
 "H_000007", "摄像机未处于待绑定状态硬件固件跟服务端交互的时候 mac地址没传给server(摄像头专有)"
 "H_000008", "设备绑定失败(未知原因)"
 
 当 code == 2000005 时，代表当前设备已被其他用户绑定，从uSDK 9.5.0版本开始，在userInfo中，可通过key：DEVICE_ID, 获取当前设备的deviceId.

 @param bindInfo 安防摄像头二维码信息  ssid和productCode为必填参数
 @param QRCodeImageCallback 二维码的回调
 @param success 配网成功的回调
 @param failure 配网失败的回调
 @since 5.4.0

 */
+ (void)bindDeviceByCameraScanQRCode:(uSDKCameraScanQRCodeBindInfo *)bindInfo
                 QRCodeImageCallback:(void(^)(UIImage *QRImage))QRCodeImageCallback
                             success:(void(^)(uSDKDevice *device))success
                             failure:(void(^)(NSError *error))failure;


/// 取消正在进行的摄像头扫码绑定接口
///  @since 10.8.0
+ (void)cancelCurrentCameraScanQRCodeBind;

/**
 根据设备ID查询绑定用户手机号码后四位
 
 @param deviceId 已绑设备ID
 @param success  接口成功回调，返回用户手机号码后四位
 @param failure  接口失败回调
 
 @since 9.5.0
 */
+ (void)getBindUserPhoneNumSuffix:(NSString *)deviceId onSuccess:(void(^)(NSString *_Nonnull phoneCode))success onFailure:(void (^)(NSError *_Nonnull))failure;

/**
网关子机设备与网关配对，并将网关子机设备绑定到云平台
调用该接口前，需要成功调用uSDKDeviceManager中的connectToCloud接口

@warning 配网过程中APP进入后台可能会导致配网失败
@param bindInfo 配置信息
@param progressNotify 进度回调，共三个进度回调：
    1. uSDKBindProgressStartEnterNetworkingMode
    2. uSDKBindProgressStartNetworking
    3. uSDKBindProgressStartBindSlaveDevice
@param success 配置绑定成功时的block回调，只有配置和绑定都成功会通过success block进行回调
@param failure 配置绑定失败时的block回调，任何一个过程失败都会通过failure block进行回调
    uSDKBindProgresStartNetworkingMode：开始进入组网模式阶段的错误码：
    参数为空  UGW_RSP_INVALID_PARAM -25001
    设备类型不支持 UGW_RSP_INVALID_PRODUCT_CODE  -25023
    配对模式已开启，重复开启 UGW_RSP_REPEAT_STARTPAIR  -25024
    开启配对失败 UGW_RSP_STARTPAIR_FAILED  -25022
    App进入后台导致的超时 ERR_USDK_ENTER_BACKGROUND_TIMEOUT -13026
 
    uSDKBindProgresStartNetworking：开始组网阶段的错误码：
    配对超时 UGW_RSP_PAIR_TIMEOUT -25026
      配对设备个数达上限 UGW_RSP_PAIR_DEV_MAX      -25025

    uSDKBindProgresStartBindSlaveDevice：开始绑定子机阶段的错误码：
    添加并绑定设备失败  UGW_RSP_BIND_ERROR -25027
    退出配对模式（人为操作设备或APP取消配网）UGW_RSP_EXIT_PAIR -25028
 */
+ (void)bindSlaveDeviceWithBindInfo:(uSDKSlaveDeviceBindInfo *)bindInfo
                     progressNotify:(void(^)(uSDKBindProgressInfo *bindProgressInfo))progressNotify
                            success:(void(^)(uSDKDevice *device))success
                            failure:(void(^)(NSError *error))failure;



/// 网关子机设备批量绑定接口
/// @param bindInfo 绑定信息，
/// @note 1.除了标注必填字段外，extendInfo在此接口必填，参考格式{"batchbind":"1","timeout":"300"}
///       2.bindInfo里面的timeoutInterval，时长校验范围30 - 600s
/// @param progressNotify 通知上报，
/// @note uSDKBindProgressBindDeviceSuccess
///       uSDKBindProgressStartEnterNetworkingMode
///       uSDKBindProgressStartNetworking
///       uSDKBindProgressStartBindSlaveDevice
///       只有批量绑定上报进度为uSDKBindProgressBindDeviceSuccess时，会上报设备对象device
/// @param completionHandler 完成回调
/// @note 1.超时完成和主动停止完成，错误信息都为空。
///       2.发起配对失败或参数校验失败时错误信息不为空。
/// @since 8.9.0
+ (void)bindSlaveDevicesWithBindInfo:(uSDKSlaveDeviceBindInfo *)bindInfo
                      progressNotify:(void (^)(uSDKBindProgressInfo * bindProgressInfo,uSDKDevice *device))progressNotify
                   completionHandler:(void (^)(NSError * error))completionHandler;


/// 批量绑定停止接口
/// 调用此接口，重试3次通过云端下发退出配对命令告诉设备
/// @param success 成功回调，1.批量绑定完成后，调用此接口，会直接返回成功  2.云端下发退出配对命令成功后，会回调成功
/// @param failure 失败回调，重试3次后云端退出配对模式均失败时，会有error，具体error取决于云端的错误码
/// @note 调用此接口时，会立即完成批量绑定。
/// @since 8.9.0
+ (void)stopBindSlaveDevices:(void(^)(void))success
                     failure:(void(^)(NSError *error))failure;



/**
一键快连 配置绑定接口，
 
 注意：配网过程中不能进行前后台切换，否则会出现实际设备配网成功，但uSDK不能接收到配网进度，认为配网失败的问题。
 
 失败的回调中 NSError对象的 code字段 可取值为：
    ERR_USDK_UNSTARTED，                                            //uSDK未启动
    ERR_USDK_CALL_CONNECT_TO_CLOUD_INTERFACE_FIRST，     //没有远程登录
    ERR_USDK_INVALID_PARAM，                                      //无效的参数
    ERR_USDK_DEVICE_CONFIG_IN_PROGRESS,           //设备正在配置中
    ERR_USDK_TIMEOUT,                                                     //配置超时
    ERR_USDK_NOT_SUPPORT_ONE_KEY_CONNECT    //设备不支持一键快连
  
  @param bindInfo 需要一键快连配置的设备info
  @param progressNotify  配置进度回调
  @param completionHandler 配网结束的回调，如果error有值，则配网失败或部分失败，error值为空，则配网全部成功
  @since 8.8.0
 */
+ (void)bindDeviceByOneKeyConnect:(uSDKOneKeyConnectBindInfo *)bindInfo
                   progressNotify:(void(^)(uSDKBindProgressInfo *bindProgressInfo))progressNotify
                completionHandler:(void(^)(NSError *error))completionHandler;

/**
 开启绑定时间窗
 调用该接口，传入正确的安住家庭的App_ID、App_Key，接口返回绑定时间窗开启成功；
 传入错误的App_ID、App_Key，接口返回绑定时间窗开启失败。
 
 @param appid 家庭的App_ID
 @param appkey 安住家庭的App_Key
 @param timeout 超时时间（单位是秒，范围为5秒-120秒），建议为15秒
 @param success 开启成功时的block回调
 @param failure 开启失败时的block回调
 */
+ (void)openUserBindTimeWindowWithAppID:(NSString*)appid
                                 appKey:(NSString*)appkey
                                timeout:(NSTimeInterval)timeout
                                success:(void(^)(void))success
                                failure:(void(^)(NSError *error))failure;

/**
 非安全设备获取bindInfo
 调用该接口前需要调用连接用户网关接口
 ```
 - (BOOL)setUserInfo:(uSDKUserInfo *)userInfo error:(NSError **)error;
 ```
 @param success 成功时的block回调
 @param failure 失败时的block回调
 @since 5.8.1
 */
+ (void)unSafeDeviceBindInfoWithSuccess:(void(^)(NSString *bindInfo))success
                                failure:(void(^)(NSError *error))failure;

/**
 新直连设备手动确认方式绑定接口

 @param bindInfo 配置信息
 @param progressNotify 进度上报，共四个进度上报
    1.参数校验成功后上报uSDKBindProgressConnectDevice
    2.与设备建立连接成功时上报uSDKBindProgressSendConfigInfo
    3.设备开始校验时上报uSDKBindProgressVerification
    4.设备开始绑定时上报uSDKBindProgressBindDevice
 @param success 配网成功时的block回调
 @param failure 配网失败时的block回调
 @since 6.0.0
 */
+ (void)bindNewDirectLinkDeviceWithManualConfirmBindInfo:(uSDKNewDirectLinkManualConfirmBindInfo *)bindInfo
           progressNotify:(void(^)(uSDKBindProgressInfo *bindProgressInfo))progressNotify
                  success:(void(^)(uSDKDevice *device))success
                  failure:(void(^)(NSError *error))failure;

/**
 新直连设备验证码校验方式绑定接口

 @param bindInfo 配置信息
 @param progressNotify 进度上报，共四个进度上报
 1.参数校验成功后上报uSDKBindProgressConnectDevice
 2.与设备建立连接成功时上报uSDKBindProgressSendConfigInfo
 3.设备开始校验时上报uSDKBindProgressVerification
 4.设备开始绑定时上报uSDKBindProgressBindDevice

 @param success 配网成功时的block回调
 @param failure 配网失败时的block回调
 @since 6.0.0
 */
+ (void)bindNewDirectLinkDeviceWithVerificationCodeBindInfo:(uSDKNewDirectLinkVerificationCodeBindInfo *)bindInfo
           progressNotify:(void(^)(uSDKBindProgressInfo *bindProgressInfo))progressNotify
                  success:(void(^)(uSDKDevice *device))success
                  failure:(void(^)(NSError *error))failure;


/**
 新直连设备 直连\非直连子机无验证绑定接口
注意： 如果绑定设备返错：设备未开启绑定时间窗，需要给设备重新上电
 
 @param bindInfo 配置信息
 @param progressNotify 进度上报，共2个进度上报
 1.设备开始绑定时上报uSDKBindProgressBindDevice
 2.设备绑定成功时上报uSDKBindProgressBindDeviceSuccess

 @param success 配网成功时的block回调
 @param failure 配网失败时的block回调
 @since 8.12.0
 */
+ (void)bindNewDirectLinkDeviceWithoutVerificationBindInfo:(uSDKNewDirectLinkWithoutVerificationBindInfo *)bindInfo
           progressNotify:(void(^)(uSDKBindProgressInfo *bindProgressInfo))progressNotify
                  success:(void(^)(uSDKDevice *device))success
                  failure:(void(^)(NSError *error))failure;


/**
绑定重试接口
当错误码为ERR_USDK_BIND_TIMEOUT_NEED_RETRY_BIND（-16018）时需要调用重试接口试图重新绑定设备，
会返回-16018的接口bindDeviceByBLE、bindPureBLEDevice、bindDeviceBySoftAp、bindDeviceBySmartLink、bindDeviceByQRCode、bindNewDirectLinkDevice

@param timeoutInterval 绑定超时时间（单位是秒，范围为10秒-180秒）
@param success 绑定成功时的block回调
@param failure 绑定失败时的block回调
@since 6.0.0
*/
+ (void)retryBindDeviceWithTimeoutInterval:(NSTimeInterval)timeoutInterval
                                   success:(void(^)(uSDKDevice *device))success
                                   failure:(void(^)(NSError *error))failure;


/**
 蓝牙mesh设备配置绑定接口，根据uSDKDeviceInfo中的configType属性，判断是否支持蓝牙mesh配网，如果是，则可以通过该接口进行配置绑定
 configType & uSDKDeviceConfigTypeBLEMesh == 1，表示支持蓝牙mesh配网
 @param bindInfo 配网参数
 @param progressNotify 进度回调，共三个进度回调：
    1. uSDKBindProgressConnectDevice
    2. uSDKBindProgressSendConfigInfo
    3. uSDKBindProgressBindDevice
 @param success 配网成功时的block回调
 @param failure 配网失败时的block回调
 
设备mesh信息分配失败。ERR_USDK_MESH_DEVICE_INFO_REQUEST_FAIL = -16008,
云连接离线 ERR_USDK_CLOUD_OFFLINE = -12005,（获取设备mesh信息的时候 如果SDK连云失败，不进行重试，直接返回失败回调）
设备provision 失败。ERR_USDK_MESH_DEVICE_PROVISION_FAIL = -16009,
设备configuration失败。ERR_USDK_MESH_DEVICE_CONFIGURATION_FAIL = -16020,（调用RTK configuration接口失败，如果有明确的错误，会直接把RTK的错误返回给APP）
绑定设备超时。ERR_USDK_BIND_DEVICE_TIMEOUT = -16005,
获取绑定结果超时。 ERR_USDK_GET_BIND_RESULT_TIMEOUT = -13024,
超出绑定设备数量上限：error.code = 301或20904
 @since 7.0.0
 */
+ (void)bindBLEMeshDevice:(uSDKBLEMeshBindInfo *)bindInfo
           progressNotify:(void(^)(uSDKBindProgressInfo *bindProgressInfo))progressNotify
                  success:(void(^)(uSDKDevice *device))success
                  failure:(void(^)(NSError *error))failure;


/// mesh设备批量绑定
/// @param bindInfo 配网参数
/// @param progressNotify 每个设备的结果
/// @param completionHandler 完成的回调
/// @since 9.6.0
+ (void)bindBLEMeshDevices:(uSDKBLEMeshBatchBindInfo*)bindInfo
            progressNotify:(void(^)(uSDKBLEMeshBindProgressInfo *bindProgressInfo))progressNotify
         completionHandler:(void(^)(NSError *error))completionHandler;


/**
蓝牙广播设备 配置绑定接口，根据uSDKDeviceInfo中的configType属性，判断是否支持蓝牙广播配网，如果是，则可以通过该接口进行配置绑定
configType & uSDKDeviceConfigTypeBLEADV == 1，表示支持蓝牙广播配网
@param bindInfo 配网参数
@param progressNotify 进度回调，共一个进度回调：
1. uSDKBindProgressBindDevice（开始绑定设备）
@param success 配网成功时的block回调
@param failure 配网失败时的block回调
 
failuer回调的Error：
保存设备版本信息失败。ERR_USDK_SAVE_DEVICE_VERSION_FAIL = -16024,
绑定超时需要重试绑定。ERR_USDK_BIND_TIMEOUT_NEED_RETRY_BIND = -16018,
获取绑定结果超时。 ERR_USDK_GET_BIND_RESULT_TIMEOUT = -13024,

@since 8.0.0
 */
+ (void)bindBLEADVDevice:(uSDKBLEADVBindInfo *)bindInfo
          progressNotify:(void(^)(uSDKBindProgressInfo *bindProgressInfo))progressNotify
                 success:(void(^)(uSDKDevice *device))success
                 failure:(void(^)(NSError *error))failure;

/**
当WiFi网络中存在已入网设备或当前连接的路由器为海尔畅连路由器时，需要配置另外一台设备入网时，可以通过此接口获取该WiFi网络的路由器SSID和密码
 1. 设备查找逻辑，找到本地列表中第一个支持获取配网信息的设备
 2. 该功能依赖设备小循环连接成功，所以在刚切网时，调用该接口，会大概率失败
  
@param timeoutInterval 超时时间(s)，[1, 60]
@param success 成功时的block回调
 uSDKConfigRouterInfo：路由器信息：
 1. ssid
 2. bssid
 3. password
 4. ssid5G
 5. bssid5G
 6. password5G
 7.isOneKeyAutoConfigWiFi
 8. isNeedSwitchNetwork
    YES：需要切网
    NO：不需要切网
    
    在获取路由器信息成功后，如果获取到的路由器信息中bssid和bssid5G都存在，直接返回NO，默认不需要切网；bssid和bssid5G不同时存在时，会与手机当前连接的wifi的ssid和bssid进行比对，如果ssid&&bssid都一致，则为NO，表示不需要切网；其他情况为YES
    如果isNeedSwitchNetwork == YES，表示手机连接为5G频段：
        1.如果ssid与当前路由器ssid相同，则表示为双频合一路由器，尽量不要走smartLink配网，会有大概率失败，其他配网方式可以直接将该信息发送给设备
        2. 如果ssid与当前路由器ssid不同
            2.1 如果只能走smartLink配网，则可以提示用户切换到2.4G频段的ssid上
            2.2 如果走SoftAp配网，则可以提示用户设备会被配到2.4G频段的ssid上，用户在从设备热点切回目标ssid时，需要切换到2.4G频段的这个ssid上
            2.3 如果走其他配网，目前不涉及手机切网，可以提示用户设备会被配到2.4G频段的ssid上
@param failure 失败时的block回调
 failuer回调的Error：
 uSDK无效参数。ERR_USDK_INVALID_PARAM = -10001，
 不支持获取配置的路由器信息的操作。ERR_USDK_GET_CONFIG_ROUTERINFO_OPERATION_NOT_SUPPORT = -16021,
 获取配置的路由器信息失败。ERR_USDK_GET_CONFIG_ROUTERINFO_FAIL = -16022,
 获取配置的路由器信息超时。ERR_USDK_GET_CONFIG_ROUTERINFO_TIMEOUT = -16023,
@since 9.0.0
 */
+ (void)getConfigRouterInfo:(NSTimeInterval)timeoutInterval
                    success:(void(^)(uSDKConfigRouterInfo *routerInfo))success
                    failure:(void(^)(NSError *error))failure;

/**
 无路由先绑定接口
 
 通过uSDKDeviceScanner发现的uSDKDeviceInfo类型的对象，如果uSDKDeviceInfo.allConfigTypes & uSDKDeviceConfigTypeBindWithoutWifi != 0，表示该设备支持无路由先绑的配网方式，可以调用下面接口进行无路由先绑定
 
 
 @param bindInfo 配置信息
 @param progressNotify 进度回调，共三个进度回调：
    1. uSDKBindProgressConnectDevice
    2. uSDKBindProgressSendConfigInfo
    3. uSDKBindProgressBindDevice
 @param completionHandler 配网完成时的block回调，如果成功，则error == nil

 uSDK未启动。 ERR_USDK_UNSTARTED = -10002
 请先调用connectToCloud接口。 ERR_USDK_CALL_CONNECT_TO_CLOUD_INTERFACE_FIRST = -14010
 蓝牙未打开。 ERR_USDK_BLE_NOT_OPEN = -13021
 uSDK无效参数。 ERR_USDK_INVALID_PARAM = -10001
 设备正在绑定中。 ERR_USDK_DEVICE_IS_IN_BINDING =-16001
 获取sessionKe失败。ERR_USDK_CLOUD_COMMON_ERROR = -10008,
 获取sessionKey请求超时。ERR_USDK_HTTP_COMMON_ERROR = -10010，
 设备不在配网状态。 ERR_USDK_DEVICE_IS_NOT_CONFIG_STATE = -13028
 设备不支持先绑后配模式   ERR_USDK_UNSUPPORT_WITHOUT_WIFI_BINDING = 13034
 蓝牙开关被关闭。 ERR_USDK_BLE_IS_POWER_OFF = -14037
 绑定过程中 可能出现的错误码：
    @(1001):@"路由器断电等导致找不到路由器",
    @(1002):@"用户修改路由信息导致无法连上路由器",
    @(1003):@"疑似密码错误",
    @(1004):@"设备未配置WiFi信息",

    @(2001):@"收到配置信息但是找不到路由",
    @(2002):@"密码错误",
    @(2003):@"疑似密码错误",
    @(2004):@"配置信息不完整",

    @(4001):@"连接主网关失败",
    @(4002):@"连接接入网关失败",

    @(5001):@"设备被解绑",
    @(5002):@"绑定达到上限",
    @(5003):@"Token失效"
 
    设备断开，连接失败。CAE_BLE_CFG_ERR_CNT_FAIL = -932
    设备异常，设备不支持此配置方式。CAE_BLE_CFG_ERR_PROFILE = -933
    写入配置数据失败。CAE_BLE_CFG_ERR_WRITE_FAIL = -934
    蓝牙设备配置失败。CAE_BLE_CFG_ERR_CFG_FAIL = -939
    蓝牙设备收到请求，但是拒绝响应。CAE_BLE_CFG_ERR_REJECT = -940
    蓝牙设备不处于配置状态。 CAE_ERROR_MODE = -10
 @since 8.6.0
 */
+ (void)bindDeviceWithoutWifi:(uSDKWithoutWifiBindInfo *)bindInfo
               progressNotify:(void(^)(uSDKBindProgressInfo *bindProgressInfo))progressNotify
            completionHandler:(void(^)(uSDKDevice *device, NSError *error))completionHandler;


/**
 蜂窝类设备绑定接口

 @param bindInfo 蜂窝设备绑定信息类
 @param progressNotify 绑定进度上报，
    totalDevicesCount：一共需要绑定的设备数量，
    bindDevicesCount：已经绑定成功的设备数。 任意一个数值发生变化的时候，都会再次主动回调给app。
 @param completionHandler 等超时时间到的时候，绑定结果的回调，
    successDevices：绑定成功的设备列表，
    failureDevices：包含失败信息的绑定失败设备列表。
    error: error有值，代表接口执行失败，参数校验失败或云端请求数据失败
 @since 8.7.0
 */
+ (void)bindCellularDevice:(uSDKCellularDeviceBindInfo *)bindInfo
            progressNotify:(void(^)(NSInteger totalDevicesCount, NSInteger bindDevicesCount))progressNotify
         completionHandler:(void(^)(NSArray<uSDKDevice *> *successDevices, NSArray<uSDKDeviceWithError *> *failureDevices, NSError *error))completionHandler;

/**
 会重试的绑定设备

 @param device 要绑定的设备
 @param deviceName 设备名称
 @param timeoutInterval 超时时间（20-120秒），推荐60秒
 @param traceNodeCS 链式跟踪关联的CS节点
 @param success 成功回调
 @param failure 失败回调
 @since 8.16.0
 */
+ (void)bindDevice:(uSDKDevice*)device
        deviceName:(NSString *)deviceName
   timeoutInterval:(NSTimeInterval)timeoutInterval
       traceNodeCS:(uTraceNode*)traceNodeCS
           success:(void(^)(void))success
           failure:(void(^)(NSError *error))failure;


/**
 解绑设备

 @param device 要解绑的设备
 @param timeoutInterval 超时时间（20-120秒）
 @param success 成功回调
 @param failure 失败回调
 @since 8.16.0
 */
+ (void)unbindDevice:(uSDKDevice*)device
     timeoutInterval:(NSTimeInterval)timeoutInterval
             success:(void(^)(void))success
             failure:(void(^)(NSError *error))failure;

/**
 *  云端BLE设备 RSSI配网优化策略
 *
 *  @return 是否开启
 *  @since 9.11.0
 */
+ (BOOL)getIsUseBLERSSIStrategy;

/**
 *  云端快连设备 RSSI配网优化策略
 *
 *  @return 是否开启
 *  @since 9.11.0
 */
+ (BOOL)getIsUseQCRSSIStrategy;


/// 快连绑定 连接优化
/// @param device 绑定的deviceinfo对象
/// @param bindInfo 绑定参数
/// @param progressNotify 进度回调
/// @param success 成功
/// @param failure 失败
+ (void)bindDeviceByQC:(uSDKDeviceInfo *)device
              bindInfo:(uSDKBLEBindInfo *)bindInfo
        progressNotify:(void(^)(uSDKBindProgressInfo *bindProgressInfo))progressNotify
               success:(void(^)(uSDKDevice *device))success
               failure:(void(^)(NSError *error))failure;



@end
