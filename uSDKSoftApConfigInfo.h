//
//  uSDKSoftApConfigInfo.h
//  uSDKGeneral
//
//  Created by wangbing on 2018/2/28.
//  Copyright © 2018年 beijinghaier. All rights reserved.
//

#import <Foundation/Foundation.h>

/**
 softAp配置时需要传入的参数
 */
@interface uSDKSoftApConfigInfo : NSObject
/**
 要配置到的SSID名称，必填, SSID最长支持32个字符
 */
@property (nonatomic, copy) NSString* ssid;

/**
 要配置的SSID对应的密码，必填，可以为空密码，如果有密码，则长度必须>=8 && <=64
 */
@property (nonatomic, copy) NSString* password;

/**
 配置超时时间（单位是秒，范围为30秒-120秒），默认60秒
 */
@property (nonatomic, assign) NSTimeInterval timeoutInterval;

/**
 配置方式为非安全或安全，默认为非安全
 */
@property (nonatomic, assign) BOOL security;

/**
 设备类型列表，当列表不为空时，会判断当前配置的设备类型是否在该列表中，如果不存在则配置失败。
 @deprecated 5.2.1
 */
@property (nonatomic, strong) NSArray<NSString *>* uplusIDs DEPRECATED_ATTRIBUTE;

/**
 当app进入后台时是否计算时间，默认为YES
 */
@property (nonatomic, assign) BOOL isTimerValidInBackground;

/**
 主网关域名
 */
@property (nonatomic, copy) NSString* mainGatewayDomain;

/**
 主网关端口
 */
@property (nonatomic, assign) NSInteger mainGatewayPort;

/**
 可取值 JP(日本) CN(中国) EU(欧洲) US(美国） WW(世界)
 */
@property (nonatomic, copy) NSString *country;

- (instancetype)initWithSSID:(NSString *)ssid
                    password:(NSString *)password NS_DESIGNATED_INITIALIZER;

- (BOOL)isValid:(NSError **)outError;


@end
