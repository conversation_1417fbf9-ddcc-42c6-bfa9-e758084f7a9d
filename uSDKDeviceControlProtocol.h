//
//  uSDKDeviceControlProtocol.h
//  uSDKDeviceLocal
//
//  Created by liugn on 2016/9/28.
//  Copyright © 2016年 haier. All rights reserved.
//

#import <Foundation/Foundation.h>

@class uSDKMutableDevice,PMDeviceAttrReadReq,PMDeviceAttrReadResp,PMDeviceAttrWriteReq,PMDeviceAttrWriteResp,PMDeviceOperReq,PMDeviceOperResp,uSDKCommandPackage,uSDKRequestDataInfo;

//对内暴露的设备对象接口
@protocol uSDKDeviceControlProtocol <NSObject>

@optional

-(void)connectWithDevice:(uSDKMutableDevice*)device error:(NSError **) error;

-(void)disconnectWithDevice:(uSDKMutableDevice*)device error:(NSError **) error;

-(void)sendMsg2ServerWithData:(uSDKCommandPackage*)package ;

////addbytsc
//-(void)connectWithDevice:(uSDKMutableDevice*)device
//                 success:(void(^)(void))success
//                 failure:(void(^)(NSError *error))failure;
//
//-(void)disconnectWithDevice:(uSDKMutableDevice*)device
//                    success:(void(^)(void))success
//                    failure:(void(^)(NSError *error))failure;

//- (void)connectDeviceByBLE:(uSDKMutableDevice*)device
//                   success:(void(^)(void))success
//                   failure:(void(^)(NSError *error))failure;
//
//- (void)disconnectDeviceByBLE:(uSDKMutableDevice*)device
//                      success:(void(^)(void))success
//                      failure:(void(^)(NSError *error))failure;

@end


