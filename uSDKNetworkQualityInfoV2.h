//
//  uSDKNetworkQualityInfoV2.h
//  uSDK
//
//  Created by 王兵 on 2020/7/20.
//  Copyright © 2020 haier. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

/**
 uSDK与设备的连接状态
 @since 6.1.0
 */
typedef NS_ENUM(NSUInteger, uSDKDeviceConnectStatus) {
    /**
     大循环在线
     @since 6.1.0
     */
    uSDKDeviceConnectStatusCloudConnected,
    /**
     大循环不在线、小循环在线
     @since 6.1.0
     */
    uSDKDeviceConnectStatusLocalConnected,
    /**
     大小循环均不在线，BLE在线
     @since 6.1.0
     */
    uSDKDeviceConnectStatusBleConnected,
    /**
     大小循环、BLE均不在线
     @since 6.1.0
     */
    uSDKDeviceConnectStatusOffline
};

/**
 设备网络质量查询结果，其中connectStatus为uSDK与设备当前的连接状态，其他属性均为云平台返回的结果
 */
@interface uSDKNetworkQualityInfoV2 : NSObject
/**
 uSDK与设备的连接状态
 @since 6.1.0
 */
@property (nonatomic, assign) uSDKDeviceConnectStatus connectStatus;
/**
设备id标识
@since 6.1.0
*/
@property (nonatomic, copy) NSString *machineId;
/**
 设备在线状态，YES：在线；NO：离线
@since 6.1.0
*/
@property (nonatomic, assign) BOOL isOnLine;
/**
 设备状态变化时间
@since 6.1.0
*/
@property (nonatomic, assign) NSTimeInterval statusLastChangeTime;
/**
 设备连接网络类型，如：wifi
@since 6.1.0
*/
@property (nonatomic, copy) NSString *netType;
/**
设备连接的网络名称
@since 6.1.0
*/
@property (nonatomic, copy) NSString *ssid;
/**
网络信号强度，最大值4，最小值-96
@since 6.1.0
*/
@property (nonatomic, assign) NSInteger rssi;
/**
网络信号强度百分比
@since 6.1.0
*/
@property (nonatomic, assign) NSInteger prssi;
/**
网络信号质量等级，0：未知；1：优；2：良；3：合格；4：差
@since 6.1.0
*/
@property (nonatomic, assign) NSInteger signalLevel;
/**
广域网丢包率
@since 6.1.0
*/
@property (nonatomic, assign) NSInteger ilostRatio;
/**
广域网延时(ms)
@since 6.1.0
*/
@property (nonatomic, assign) NSInteger its;
/**
内网ip
@since 6.1.0
*/
@property (nonatomic, copy) NSString *lanIP;
/**
模块版本描述，版本格式：软件版本号/软件类型/硬件版本号/硬件类型
@since 6.1.0
*/
@property (nonatomic, copy) NSString *moduleVersion;
@end

NS_ASSUME_NONNULL_END
