//
//  uSDKDeviceBase.h
//  uSDKDeviceLocal
//
//  Created by liugn on 2016/10/18.
//  Copyright © 2016年 haier. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "uSDKDeviceInfo.h"
#import "uSDKDeviceControlProtocol.h"
#import "uSDKArgument.h"
#import "uSDKDeviceProtocol.h"
#import "uSDKDeviceAlarm.h"
#import "uTrace.h"
#import "uSDKModuleInfo.h"
#import "uSDKNetworkQualityInfoV2.h"
#import "uSDKDeviceEvent.h"
#import "uSDKDeviceConstantInfo.h"
#import "uSDKCommandInfo.h"

#define TIME_OUT 5.0

@class uSDKSubDevice, uSDKBLEMeshChannel;
@class uSDKNetworkQualityInfo;
@class uSDKFOTAInfo;
@class uSDKFOTAStatusInfo;
@class uSDKOTAStatusInfo;
@class uSDKFaultInformation;
@class uSDKDeviceFOTAInfo, uSDKFOTAAbilityInfo;
@class uSDKRouterInfo, uSDKDeviceVersionInfo, uSDKQCConnectInfo,uSDKUMeshPairInfo, uSDKUMeshPairDeviceInfo, uSDKUMeshPairStateAndList;



/**
 * uSDK设备实体类，用于描述设备的基本信息、网络信息和网络状态，缓存当前的运行状态和报警，提供执行设备操作能力，接收属性上报。
 */
@interface uSDKDevice : uSDKDeviceInfo<uSDKDeviceProtocol>{
    @public
    NSMutableDictionary* _attributeDict;
    NSMutableArray* _alarmList;
}

/**
 *  设备代理，用于接收回调消息（设备状态变化、属性变化、报警等）
 */
@property (nonatomic, weak) id<uSDKDeviceDelegate> delegate;

/**
 *   设备属性状态集合。设备属性状态是指每种设备所特有的运行时状态，例如空调的当前环境温度、洗衣机当前剩余的洗涤时间等。每一种设备都有多个功能状态，在这个集合中，可以以属性名称为关键字，找到对应的属性状态；
 */
@property (nonatomic, strong ,readonly) NSDictionary<NSString*,uSDKDeviceAttribute*>* attributeDict;

/**
 *   设备报警信息集合。设备每次报警时，会上报一条或多条报警信息，uSDK会将这些报警信息放入报警列表中。uSDK只保存设备最近一次发生的报警；
 */
@property (nonatomic, strong ,readonly) NSArray<uSDKDeviceAlarm*>* alarmList;

/**
 *  设备的子设备列表（商空）
 */
@property (nonatomic, strong , readonly) NSArray<uSDKSubDevice*>* subDeviceList;

/**
 组设备的成员列表
 @since 8.4.0
 */
@property (nonatomic, strong, readonly) NSArray<uSDKDevice *> *groupMembers;

/**
 * 设备休眠状态
 * sleepState 默认未休眠 uSDKDeviceSleepStateUnsleeping = 0
 * @since v8.10.0 添加设备休眠状态,
 */
@property (nonatomic, assign, readonly) uSDKDeviceSleepState sleepState;

/**
 * 主动离线原因
 * activeOfflineCause 默认0
 * @since v9.12.0 添加设备主动离线原因
 */
@property (nonatomic, assign, readonly) uSDKDeviceActiveOfflineCause activeOfflineCause;

/**
 设备的在线状态
 1. 如果云端在线或Wi-Fi可以搜索到或BLE可以搜索到表示设备在线
 2. 在线不代表设备可控，只表示设备的在离线状态
 3. 与设备是否订阅无关
 
 @since 8.10.0
 */
@property (nonatomic, assign, readonly) uSDKDeviceOnlineState onlineState;

/**
 设备的在离线状态v2
 1. 如果云端在线或本地可以搜索到设备，且设备已就绪，状态为在线就绪
 2. 如果云端在线或本地可以搜索到设备，但设备未就绪，状态为在线未就绪
 3. 其他状态为离线状态
 
 @since 8.12.0
 */
@property (nonatomic, assign, readonly) uSDKDeviceOnlineStateV2 onlineStateV2;

/**
 设备的WIFI在离线状态
 该状态 只包含设备的 wifi小循环 和 wifi大循环 融合在线状态
 @since 10.0.0
 */
@property (nonatomic, assign, readonly) uSDKDeviceOnlineState wifiOnlineState;

/**
 设备的仅配网状态
 @since 10.1.0
 */
@property (nonatomic, assign, readonly) uSDKDeviceOnlyConfigState onlyConfigState;

/**
 设备是否支持仅配网
 @since 10.6.0
 */
@property (nonatomic, assign, readonly) BOOL isSupportOnlyConfig;

/**
设备是否要走仅配网流程
@since 10.6.0
 */
@property (nonatomic, assign, readonly) BOOL isOnlyConfigFlow;

/**
 设备的配置状态码
 @since 10.1.0
 */
@property (nonatomic, assign, readonly) NSInteger configStateCode;

/**
 设备本地wifi连接状态。
 @since 8.11.0
*/
@property (nonatomic, assign, readonly) uSDKDeviceState localState;

/**
 设备蓝牙连接状态。
 @since 8.11.0
*/
@property (nonatomic, assign, readonly) uSDKDeviceState bleState;

/**
 设备远程连接状态。
 @since 9.5.0
*/
@property (nonatomic, assign, readonly) uSDKDeviceState remoteState;

/**
 * 设备网络质量等级
 *
 * @since v8.12.0
 */
@property (nonatomic, assign, readonly) uSDKDeviceNetQualityLevel qualityLevel;

/**
 *设备blemesh相关能力
 *
 * @since v9.6.0
*/
@property (nonatomic, strong) uSDKBLEMeshChannel *bleMeshChannel;

/**
 设备blemesh连接状态。
 @since 9.6.0
*/
@property (nonatomic, assign, readonly) uSDKDeviceState blemeshState;


/**
 *  连接设备，连接后会收到该设备的相关消息。需要对设备对象设置delegate，才能收到相应设备的消息。注意：调用该方法连接的设备没有就绪状态。
 *
 *  @param success   成功回调
 *  @param failure   失败回调
 */
-(void)connectWithSuccess:(void(^)(void))success
                  failure:(void(^)(NSError *error)) failure;

/**
 *  连接设备同时自动查询设备的所有属性，连接后会收到该设备的相关消息。需要对设备对象设置delegate，才能收到相应设备的消息。注意：调用该方法连接的设备有就绪状态。
 *
 *  @param success 成功回调
 *  @param failure 失败回调
 */
-(void)connectNeedPropertiesWithSuccess:(void(^)(void))success
                                failure:(void(^)(NSError *error)) failure;

/// 快连专用连接接口
/// @param success 成功回调
/// @param failure 失败回调
/// @since 8.15.0
- (void)QCConnectWithSuccess:(void(^)(void))success
                     failure:(void(^)(NSError *error))failure;

/// 快连专用连接接口8.15.0不可用
/// @param success 成功回调
/// @param failure 失败回调
/// @param timeout  超时回调
/// @since 8.13.0
- (void)QCConnectWithSuccess:(void(^)(void))success
                     failure:(void(^)(NSError *error))failure
                   onTimeOut:(void(^)(uSDKQCConnectTimeoutType timeoutType))timeout __attribute__((deprecated("8.15.0不可用，8.16.0移除")));



/// 快连专用连接接口，新增连接信息参数
/// @param connectInfo 连接信息参数
/// @param success 成功回调
/// @param failure 失败回调
/// @since 10.4.0
- (void)QCConnectWithConnectInfo:(uSDKQCConnectInfo*)connectInfo
                         success:(void(^)(void))success
                         failure:(void(^)(NSError *error))failure;


/// 快连专用断开连接接口
/// @param success 成功回调
/// @param failure 失败回调
/// @since 8.13.0
- (void)QCDisconnectWithSuccess:(void(^)(void))success
                        failure:(void(^)(NSError *error)) failure;

/**
 *  断开设备连接
 *
 *  @param success   成功回调
 *  @param failure   失败回调
 */
-(void)disconnectWithSuccess:(void(^)(void))success
                     failure:(void(^)(NSError *error)) failure;

/**
 *  设备订阅同步接口，查找到设备对象后，标记为订阅后，立即返回，理论上不会阻塞耗时
 *
 *  @warning 重复订阅也认为成功
 *
 *  @return NSError 错误对象
 *   - 成功时返回 nil
 *
 *  @since 10.5.1
 */
- (NSError *)connectNeedPropertiesSync;

#pragma mark - device control
/**
 *  根据属性名读取设备的属性值，属性值会在回调函数中返回，并更新设备对应的属性值。
 *
 *  @param name    属性名
 *  @param success 成功，返回属性值
 *  @param failure 失败
 */
-(void)readAttributeWithName:(NSString*)name
                     success:(void(^)(NSString *value)) success
                     failure:(void(^)(NSError *error)) failure;

/**
 *  根据属性名读取设备的属性值，属性值会在回调函数中返回，并更新设备对应的属性值。
 *
 *  @param name    属性名
 *  @param timeoutInterval 超时时间（12-120秒）
 *  @param success 成功，返回属性值
 *  @param failure 失败
 */
-(void)readAttributeWithName:(NSString*)name
             timeoutInterval:(NSTimeInterval)timeoutInterval
                     success:(void(^)(NSString *value)) success
                     failure:(void(^)(NSError *error)) failure;

/**
 *  根据属性名读取设备的属性值，属性值会在回调函数中返回，并更新设备对应的属性值。 （uSDK5.0.01 新增）
 *
 *  @param name    属性名
 *  @param trace  链式跟踪打点的trace
 *  @param timeoutInterval 超时时间（5-120秒）
 *  @param success 成功，返回属性值
 *  @param failure 失败
 */
-(void)readAttributeWithName:(NSString*)name
                      uTrace:(uTrace *)trace
             timeoutInterval:(NSTimeInterval)timeoutInterval
                     success:(void(^)(NSString *value)) success
                     failure:(void(^)(NSError *error)) failure;
/**
 *  写入设备属性值,回调中只返回是否成功,如果写入成功，设备对应的属性值会在设备属性变化上报中更新。注：此方法可用于发送单命令.
 *
 *  @param name    属性名
 *  @param value   属性值
 *  @param success 成功
 *  @param failure 失败
 */
-(void)writeAttributeWithName:(NSString*)name
                        value:(NSString*)value
                      success:(void(^)(void)) success
                      failure:(void(^)(NSError *error)) failure;

/**
 *  写入设备属性值,回调中只返回是否成功,如果写入成功，设备对应的属性值会在设备属性变化上报中更新。注：此方法可用于发送单命令.
 *
 *  @param name    属性名
 *  @param value   属性值
 *  @param timeoutInterval 超时时间（5-120秒）
 *  @param success 成功
 *  @param failure 失败
 */
-(void)writeAttributeWithName:(NSString*)name
                        value:(NSString*)value
              timeoutInterval:(NSTimeInterval)timeoutInterval
                      success:(void(^)(void)) success
                      failure:(void(^)(NSError *error)) failure;

/**
 *  写入设备属性值,回调中只返回是否成功,如果写入成功，设备对应的属性值会在设备属性变化上报中更新。注：此方法可用于发送单命令. (uSDK5.0.01新增)
 *
 *  @param name    属性名
 *  @param value   属性值
 *  @param trace  链式跟踪打点的trace
 *  @param timeoutInterval 超时时间（5-120秒）
 *  @param success 成功
 *  @param failure 失败
 */
-(void)writeAttributeWithName:(NSString*)name
                        value:(NSString*)value
                       uTrace:(uTrace *)trace
              timeoutInterval:(NSTimeInterval)timeoutInterval
                      success:(void(^)(void)) success
                      failure:(void(^)(NSError *error)) failure;
/**
 *  执行设备命令操作。每一种设备都有自己特定的命令集，详细的命令集描述请参看对应的设备ID文档。
 *
 *  @param operationName   要执行的设备操作命令
 *  @param args            要执行的设备操作命令列表
 *  @param success         命令执行成功回调
 *  @param failure         命令执行失败回调
 */
- (void)executeOperation:(NSString*)operationName
                    args:(NSArray<uSDKArgument*>*)args
                 success:(void(^)(void)) success
                 failure:(void(^)(NSError *error)) failure;

/**
 *  执行设备命令操作，可自定义超时时间。每一种设备都有自己特定的命令集，详细的命令集描述请参看对应的设备ID文档。
 *
 *  @param operationName   要执行的设备操作命令
 *  @param args            要执行的设备操作命令列表
 *  @param timeoutInterval 超时时间（5-120秒）
 *  @param success         命令执行成功回调
 *  @param failure         命令执行失败回调
 */
- (void)executeOperation:(NSString*)operationName
                    args:(NSArray<uSDKArgument*>*)args
         timeoutInterval:(NSTimeInterval)timeoutInterval
                 success:(void(^)(void)) success
                 failure:(void(^)(NSError *error)) failure;

/**
 *  执行设备命令操作，可自定义超时时间。每一种设备都有自己特定的命令集，详细的命令集描述请参看对应的设备ID文档。 (uSDK5.0.01 新增)
 *
 *  @param operationName   要执行的设备操作命令
 *  @param args            要执行的设备操作命令列表
 *  @param trace          链式跟踪打点的trace
 *  @param timeoutInterval 超时时间（5-120秒）
 *  @param success         命令执行成功回调
 *  @param failure         命令执行失败回调
 */
- (void)executeOperation:(NSString*)operationName
                    args:(NSArray<uSDKArgument*>*)args
                  uTrace:(uTrace *)trace
         timeoutInterval:(NSTimeInterval)timeoutInterval
                 success:(void(^)(void)) success
                 failure:(void(^)(NSError *error)) failure;

/**
 *  设备模型2.1 控制命令
 *
 *  @param command 要执行的设备操作命令
 *  @param success 命令执行成功回调
 *  @param failure 命令执行失败回调
 *  @since X.X.X
 */
- (void)executeOperationWithCommandV2:(uSDKOperationCommand *)command
                              success:(void(^)(NSString *result))success
                              failure:(void(^)(NSError *error))failure;

/**
 *  配置模块主网关域名和端口，默认超时时间为5秒。国内主网关：gw.haier.net 端口：56808  海外主网关：gw.haieriot.net 端口：56808
 *
 *  @param domain  主网关域名
 *  @param port    端口号
 *  @param success 命令执行成功回调
 *  @param failure 命令执行失败回调
 */
- (void)setDeviceGatewayWithDomain:(NSString *)domain
                              port:(NSInteger)port
                           success:(void(^)(void)) success
                           failure:(void(^)(NSError *error)) failure;



/**
 *  获取设备信号强度信息
 *
 *  @param success 成功回调 注：其中number表示信号强度的具体数值，quality 代表强度的等级，包含 差、中、良、优
 *           差：number <= 20，
 *           中：21 <= number <= 30，
 *           良：31 <= number <= 40，
 *           优：number => 41
 *
 *  @param failure 失败回调
 */
- (void)getDeviceNetQualitySuccess:(void(^)(NSUInteger number, DeviceNetQuality quality)) success
                           failure:(void(^)(NSError *error)) failure;


/**
 对设备进行授权

 @param success 成功的回调
 @param failure 失败的回调
 */
- (void)authToDeviceSuccess:(void(^)(void)) success
                    failure:(void(^)(NSError *error)) failure;


/**
 对设备进行取消授权操作

 @param success 成功的回调
 @param failure 失败的回调
 */
-(void)authToDeviceInvalidSuccess:(void(^)(void)) success
                          failure:(void(^)(NSError *error)) failure;

/**
 获取设备的授权状态

 @param success 获取成功的回调（authState为YES时 代表已经授权，为NO时 代表未授权）
 @param failure 失败的回调
 */
-(void)getDeviceAuthStateSuccess:(void(^)(BOOL authState)) success
                         failure:(void(^)(NSError *error)) failure;



/**
 订阅资源, 如果返回ERR_USDK_DEVICE_REMOTE_STATE_NOT_READY错误，表示资源未就绪，需要重试

 @param resourceName 资源名称
 @param success 订阅成功的回调
 @param failure 订阅失败的回调
 */
- (void)subscribeResource:(NSString *)resourceName success:(void(^)(void))success failure:(void(^)(NSError *error))failure;

/**
 订阅资源新接口，数据会进行加解密， 对应的delegate回调为新的:- (void)device:(uSDKDevice *)device didReceiveDecodeResource:(NSString *)resource data:(NSString *)JSONData;


 @param resourceName 资源名称
 @param success 订阅成功的回调
 @param failure 订阅失败的回调
 @since 8.5.0
 */
- (void)subscribeResourceWithDecode:(NSString *)resourceName success:(void(^)(void))success failure:(void(^)(NSError *error))failure;


/**
 解订阅资源，如果未订阅过该资源，则返回参数错误

 @param resourceName 资源名称
 @param success 解订阅成功的回调
 @param failure 解订阅失败的回调
 */
- (void)unSubscribeResource:(NSString *)resourceName success:(void(^)(void))success failure:(void(^)(NSError *error))failure;


/**
 获取设备网络质量信息 （uSDK5.2.0 新增）

 @param success 成功的回调（uSDKNetworkQualityInfo：设备网络质量信息类，具体的网络质量指标 请进该类中查看）
 @param failure 失败的回调
 */
- (void)getNetworkQualitySuccess:(void(^)(uSDKNetworkQualityInfo *networkQuality))success
                         failure:(void(^)(NSError *error))failure;

/**
 查询设备的连接状态和网络信号质量相关信息

 @param success 成功的回调（uSDKNetworkQualityInfo：设备网络质量信息类，具体的网络质量指标 请进该类中查看）
 @param failure 失败的回调
 @since 6.1.0
 */
- (void)getNetworkQualityV2Success:(void(^)(uSDKNetworkQualityInfoV2 *networkQuality))success
                         failure:(void(^)(NSError *error))failure;
/**
 设备远程唤醒
 
 注：该方法返回成功只表示发送唤醒命令成功，并不代表设备当前已被唤醒（即：sleepState = 0）。
 当设备休眠状态变为未休眠（sleepState = 0）时，会通过uSDKDeviceDelegate代理方法：
 - (void)device:(uSDKDevice *)device didUpdateSleepState:(uSDKDeviceSleepState )sleepState;
 上报。
 
 @param success 唤醒成功回调
 @param failure 失败的回调
 @since v8.10.0 添加远程唤醒已休眠设备的接口
 */
- (void)wakeUpOnSuccess:(void(^)(void))success failure:(void(^)(NSError *error))failure;

#pragma mark - BLE History
/**
 获取蓝牙历史数据, 获取成功后，数据将通过uSDKDeviceDelegate协议的device:didReceiveBLEHistoryData:currentCount:totalCount:方法进行上报

 @param success 成功的回调
 @param failure 失败的回调
 @since 5.4.0
 */
- (void)fetchBLEHistoryDataSuccess:(void(^)(void))success
                           failure:(void(^)(NSError *error))failure;

/**
 取消获取蓝牙历史数据
 
 @param success 成功的回调
 @param failure 失败的回调
 @since 5.4.0
 */

- (void)cancelFetchBLEHistoryDataSuccess:(void(^)(void))success
                           failure:(void(^)(NSError *error))failure;

#pragma mark - FOTA
/**
 检查设备底板固件的版本信息

 @param success 成功回调（uSDKFOTAInfo ：设备固件版本信息） 注意：当isNeedFOTA字段为 YES 时，表明设备有需要更新的固件版本。当isNeedFOTA字段为 NO 时，表明设备不支持升级或没有需要更新的固件版本。
 @param failure 失败的回调
 @since 5.4.0
 */
- (void)checkBoardFOTAInfoWithSuccess:(void(^)(uSDKFOTAInfo *FOTAInfo))success
                              failure:(void(^)(NSError *error))failure;


/**
 开始设备底板固件的升级，执行成功后，设备的最新升级状态 会通过uSDKDevice的代理方法 持续上报
 - (void)device:(uSDKDevice *)device didUpdateBoardFOTAStatus:(uSDKFOTAStatusInfo *)FOTAStatusInfo;

 @param success 成功的回调
 @param failure 失败的回调
 @since 5.4.0
 */
- (void)startBoardFOTAWithSuccess:(void(^)(void))success
                          failure:(void(^)(NSError *error))failure;


/**
 查询当前设备底板固件的升级状态

 @param success 成功的回调(uSDKFOTAStatusInfo 设备升级进度信息)
 @param failure 失败的回调
 @since 5.4.0
 */
- (void)fetchBoardFOTAStatusWithSuccess:(void(^)(uSDKFOTAStatusInfo *statusInfo))success
                                failure:(void(^)(NSError *error))failure;


#pragma mark - FOTA3.0
/**
 开始设备固件升级指令，执行成功后，设备的最新升级状态 会通过uSDKDevice的代理方法 持续上报
 - (void)device:(uSDKDevice *)device didUpdateFOTAStatus:(uSDKFOTAStatusInfo *)statusInfo;

 @param deviceFOTAInfo 设备整机固件升级信息类
 @param completionHandler 接口执行完成时回调，error == nil表示下发开始升级指令成功
 @since 8.7.0  新增蓝牙FOTA升级逻辑, 8.16.0 修改新增FOTA3.0流程
 */
- (void)startFOTAWithDeviceFOTAInfo:(uSDKDeviceFOTAInfo *_Nonnull)deviceFOTAInfo
                  completionHandler:(nullable void(^)(NSError *_Nullable error))completionHandler;
 
/**
 * 获取设备FOTA能力
 *
 * @note 接口返回nil：代表当前设备不支持FOTA
 *
 * @since 8.16.0
 */
- (uSDKFOTAAbilityInfo *_Nullable)getFOTAAbility;
/**
 * 查询设备的升级进度
 * @param successCallback 成功的回调
 * @param failCallback 失败的回调
 */
- (void)getFOTAStatusOnSuccess:(nullable void (^)(uSDKFOTAStatusInfo *_Nonnull info))successCallback
                       failure:(nullable void (^)(NSError *_Nonnull error))failCallback;
/**
 用户二次确认升级接口
 
 适用于大固件升级中，需要用户二次确认升级
 
 @param traceId 链式跟踪唯一标识
 @param fwId 固件id
 @param taskStatus 表示使能该任务生效，（建议这里永远传YES, 不要传NO）
 @param callback 成功/失败回调
 
 @since 9.4.0
 */
- (void)userConfirmFOTA:(NSString *_Nonnull)traceId
                   fwId:(NSString *_Nonnull)fwId
             taskStatus:(BOOL )taskStatus
              completed:(nullable void(^)(NSError *_Nullable error))callback;


#pragma mark - Version info
/**
 获取设备整机版本信息
 
 @param success 查询成功回调
 @param failure 查询失败回调
 
 @since 9.5.0
 */
- (void)checkDeviceVersionInfoOnSuccess:(nullable void (^)(uSDKDeviceVersionInfo * _Nonnull))success failure:(nullable void(^)(NSError *_Nonnull))failure;

#pragma mark - moduleOTA

/**
 开始设备模块升级

 @param upgradeStatus 升级进度回调
 @param success 成功的回调
 @param failure 失败的回调
 */
- (void)moduleOTAWithProgress:(void(^)(uSDKOTAStatusInfo *upgradeStatus))upgradeStatus
                      success:(void(^)(void))success
                      failure:(void(^)(NSError *error))failure;

#pragma mark - TimeZone
/**
 设置设备时区
 
 时区(TimeZone)，以标准UTC时间为基准时间，单位为分钟（min）
 比标准时间晚，则为正整数
 比标准时间早，则为负整数
 
 例：北京时间为东8区时间(UTC+8)，比标准UTC时间晚8小时，则timeZone = 480 min。
 
 @since 9.6.0
 */
- (void)setUpDeviceTimeZone:(NSInteger)timeZone success:(nullable void(^)(void))success failure:(nullable void(^)(NSError *error))failure;

/**
 获取设备当前所在时区
 
 时区（TimeZone），以标准UTC时间为基准时间，单位为分钟（min）
 比标准时间晚，则为正整数
 比标准时间早，则为负整数
 
 例：北京时间为东8区时间(UTC+8)，比标准UTC时间晚8小时，则timeZone = 480 min。
 */
- (void)getDeviceTimeZoneOnSuccess:(nullable void(^)(NSInteger timeZone))success failure:(nullable void(^)(NSError *_Nonnull error))failure;

#pragma mark - uMesh switch

/**
 获取设备UMesh开关状态

 @param success 成功的回调
 @param failure 失败的回调
 @since 9.13.0
 */
- (void)getDeviceUMeshSwitchSuccess:(void(^)(BOOL UMeshSwitch))success
                           failure:(void(^)(NSError *error))failure;

/**
 设置设备的UMesh开关状态
 
 @param UMeshSwitch YES or NO 开或者关
 @param success 成功的回调
 @param failure 失败的回调
 @since 9.13.0
 */
- (void)setDeviceUMeshSwitch:(BOOL)UMeshSwitch
                     success:(void(^)(void))success
                     failure:(void(^)(NSError *error))failure;

/// 设备进配对命令下发
/// @param pairInfo 配对命令参数
/// @param success 成功回调
/// @param failure 失败回调
/// @since 10.4.0
- (void)enterUMeshPair:(uSDKUMeshPairInfo *)pairInfo
               success:(void(^)(void)) success
               failure:(void(^)(NSError *error)) failure;

/// 解除配对命令
/// @param slaveDeviceID 需要解除的设备ID
/// @param success 成功回调
/// @param failure 失败回调
/// @since 10.4.0
- (void)removeUMeshPair:(NSString *)slaveDeviceID
                success:(void(^)(void)) success
                failure:(void(^)(NSError *error)) failure;


/// 设备配对状态及已配对设备列表查询
/// @param success 成功回调，返回设备配对状态及已配对设备列表，见uSDKUMeshPairStateAndList
/// @param failure 失败回调
/// @since 10.7.0
- (void)getDeviceUMeshPairStateAndListSuccess:(void(^)(uSDKUMeshPairStateAndList *stateAndList)) success
                                      failure:(void(^)(NSError *error)) failure;

/// 设备进入搜索可配对设备模式
/// @param quit    1：先退出配对再进搜索；0：直接进搜索
/// @param timeout 搜索超时时间，单位毫秒，默认是0，最大不超过120 * 1000ms
/// @param success 成功回调
/// @param failure 失败回调
/// @since 10.7.0
- (void)startUMeshPairDeviceSearchWithQuit:(NSInteger)quit
                             searchTimeout:(NSTimeInterval)timeout
                                   success:(void(^)(void)) success
                                   failure:(void(^)(NSError *error)) failure;

/// 指定设备配对
/// @param pairDevInfo    要指定配对的设备信息，其中 devMac 必填, devId可选，typeId 无需赋值，timeout默认为 0，最大不超过 30s
/// @param timeout  配对超时时间，单位毫秒，默认是0，最大不超过30 * 1000ms
/// @param success 成功回调，成功仅表示开始配对成功
/// @param failure 失败回调
/// @since 10.7.0
- (void)startUMeshPairWithDevice:(uSDKUMeshPairDeviceInfo *)pairDevInfo timeout:(NSTimeInterval)timeout
               success:(void(^)(void)) success
               failure:(void(^)(NSError *error)) failure;

/// 指定设备配对解除
/// @param pairDevInfo    要解除配对的设备信息，其中 devMac 为必填，devId可为空，typeId 在当前接口无需填充
/// @param success 成功回调
/// @param failure 失败回调
/// @since 10.7.0
- (void)removeUMeshPairWithDevice:(uSDKUMeshPairDeviceInfo *)pairDevInfo
               success:(void(^)(void)) success
               failure:(void(^)(NSError *error)) failure;

#pragma mark - delegate
/**
 添加代理监听
 
 内部会自动调用connectNeedPropertiesWithSuccess方法订阅设备
 
 @param  delegate  需要添加的代理对象
 @since v8.8.0  添加一对多代理支持
 */
- (void)addDelegate:(id<uSDKDeviceDelegate>)delegate;

/**
 移除代理监听
 
 如果当前设备的代理全部移除，会自动调用disconnectWithSuccess方法
 
 @param  delegate  需要移除的代理对象
 @since v8.8.0  添加一对多代理支持
 */
- (void)removeDelegate:(id<uSDKDeviceDelegate>)delegate;
/**
 判断是否包含
 @param delegate 代理对象
 @since v8.8.0  添加一对多代理支持
 */
- (BOOL)containsDelegate:(id<uSDKDeviceDelegate>)delegate;

#pragma mark - WIFI DOOR LOCK

/// 调用方法使设备进入睡眠模式
/// 注：该方法返回成功，即表示睡眠成功，不需要通过代理方法判断睡眠结果
/// - Parameters:
///   - success: 成功回调
///   - failure: 失败回调
/// - since 9.5.0
- (void)enterSleepModeWithSuccess:(void(^)(void))success
                          failure:(void(^)(NSError *error)) failure;

/// 使用新协议唤醒睡眠中的设备
/// 注：该方法返回成功，即表示睡眠成功，不需要通过代理方法判断睡眠结果
/// - Parameters:
///   - success: 成功回调
///   - failure: 失败回调
/// - since 9.5.0
- (void)wakeUpV2OnSuccess:(void (^)(void))success failure:(void (^)(NSError *))failure;


/// 订阅蓝牙通路，订阅后，将保持蓝牙通路连接状态
/// 意外断开重新发现后会自动连接
/// - Parameters:
///   - success: 成功回调
///   - failure: 失败回调
/// - since 9.5.0
- (void)subscribeBLEWithSuccess:(void(^)(void))success
                          failure:(void(^)(NSError *error)) failure;

/// 解订阅蓝牙通路，解订阅后，将根据通路优先级自动处理蓝牙通路的断连状态
/// - Parameters:
///   - success: 成功回调
///   - failure: 失败回调
/// - since 9.5.0
- (void)unsubscribeBLEWithSuccess:(void(^)(void))success
                          failure:(void(^)(NSError *error)) failure;

/// 根据属性名读取设备的属性值，属性值会在回调函数中返回，并更新设备对应的属性值
/// - Parameters:
///   - command:  读取属性命令对象
///   - success: 成功回调，返回属性
///   - failure: 失败回调
- (void)readAttributeWithCommand:(uSDKReadCommand *)command
                         success:(void (^)(NSString *))success
                         failure:(void (^)(NSError *))failure;

/// 写入设备属性值,回调中只返回是否成功,如果写入成功，设备对应的属性值会在设备属性变化上报中更新。注：此方法可用于发送单命令.
/// - Parameters:
///   - command: 写属性命令对象
///   - success: 成功回调
///   - failure: 失败回调
- (void)writeAttributeWithCommand:(uSDKWriteCommand *)command
                          success:(void (^)(void))success
                          failure:(void (^)(NSError *))failure;

/// 执行设备命令操作。每一种设备都有自己特定的命令集，详细的命令集描述请参看对应的设备ID文档
/// - Parameters:
///   - command: 要执行的设备操作命令对象
///   - success: 命令执行成功回调
///   - failure: 命令执行失败回调
- (void)executeOperationWithCommand:(uSDKOperationCommand *)command
                            success:(void (^)(void))success
                            failure:(void (^)(NSError *))failure;

#pragma mark - 产测

/// 产测透传接口
/// - Parameters:
///   - data: 要下发给设备的透传数据
///   - success: 成功回调
///   - failure: 失败回调
///   - note  上传接口通过下面的代理方法上报
///    - (void)device:(uSDKDevice *)device didReceiveBLERealTimeData:(NSData *)data;
- (void)transmitData:(NSData * _Nonnull)data success:(void(^_Nullable)(void))success failure:(void(^_Nullable)(NSError * _Nonnull error))failure;

#pragma mark - FOCUS

/**
标记设备进入焦点（详情页）
 
 进入焦点后，如果大循环控制超时，则提升蓝牙通道的优先级高于大循环且低于小循环通道
@return 重复进入返回NO
@since 5.7.0
*/
- (BOOL)inFocus;

/**
标记设备退出焦点（详情页）

@return 重复退出返回NO
@since 5.7.0
*/
- (BOOL)outFocus;


#pragma mark - Group 组控
/**
 获取可与当前设备分到同一组的设备列表, 当前设备要求有zigbee能力或BLEMesh能力
  
 @param completionHandler 接口执行完成时回调，error == nil表示接口执行成功，接口执行成功时，devices也可能为空，表示接口执行成功，但没有可分组设备
 @since 8.4.0
 */
- (void)fetchGroupableDeviceListCompletionHandler:(void(^)(NSArray<uSDKDevice *> *devices, NSError *error))completionHandler;
 
/**
创建分组，返回组设备对象
 
@param timeoutInterval 超时时间，取值范围30-180秒，App可根据添加设备的多少动态调整参数
@param completionHandler 接口执行完成时回调，error == nil表示组创建成功，注意组设备虽然创建成功，但没有任何设备被成功添加到该组中，
需要主动调用addDevices:toGroupWithTimeoutInterval:progressNotify:completionHandler:函数去添加进组中。
@since 8.4.0
*/
- (void)createGroupWithTimeoutInterval:(NSTimeInterval)timeoutInterval
                                                   completionHandler:(void(^)(uSDKDevice *device, NSError *error))completionHandler;
 
 
/**
 向组设备中添加设备，要求当前device对象为组设备
 
 @param devices 需要添加到组设备的设备列表，可以添加到组设备的设备列表需要通过接口``
 @param timeoutInterval 超时时间，取值范围30-180秒，App可根据添加设备的多少动态调整参数
 @param progressNotify 进度通知，上报每个设备的添加结果，如果error == nil则表示添加成功
 @param completionHandler 接口执行完成时回调，参数校验失败时error有值，一旦开始添加，不管是否有设备成功添加到组，error都为nil
 @since 8.4.0
*/
- (void)addDevices:(NSArray<uSDKDevice *> *)devices
toGroupWithTimeoutInterval:(NSTimeInterval)timeoutInterval
    progressNotify:(void(^)(uSDKDevice *device, NSError *error))progressNotify
            completionHandler:(void(^)(NSError *error))completionHandler;
 
/**
 从组设备中移除设备，要求当前device对象为组设备
 
 @param devices 需要从组设备中删除的设备列表
 @param timeoutInterval 超时时间，取值范围30-180秒，App可根据删除设备的多少动态调整参数
 @param progressNotify 进度通知，上报每个设备的删除结果，如果error == nil则表示设备删除成功
 @param completionHandler 接口执行完成时回调，参数校验失败时error有值，一旦开始添加，不管是否有设备成功添加到组，error都为nil
 @since 8.4.0
*/
- (void)removeDevices:(NSArray<uSDKDevice *> *)devices
fromGroupWithTimeoutInterval:(NSTimeInterval)timeoutInterval
       progressNotify:(void(^)(uSDKDevice *device, NSError *error))progressNotify
                 completionHandler:(void(^)(NSError *error))completionHandler;
 
/**
 删除组设备，要求当前device对象为组设备
 
 @param completionHandler 接口执行完成时回调，error == nil表示组删除成功
 @since 8.4.0
 */
- (void)deleteGroupCompletionHandler:(void(^)(NSError *error))completionHandler;

/**
 获取设备模块信息
 
 @param timeout 超时时间(s)，最小5s，最长120s，建议值15s
 @param success 成功的回调, 返回uSDKModuleInfo对象
    
    1. 接口只要执行成功，就会通过success block返回
    2. 但uSDKModuleInfo中具体数据依赖云平台，可能有的字段是空的，可能全都是空的

 @param failure 失败的回调, 返回NSError对象
 @since 6.0.0
 */
- (void)deviceModuleInfoWithTimeout:(NSTimeInterval)timeout
                            success:(void(^)(uSDKModuleInfo *info))success
                            failure:(void(^)(NSError *error))failure;

#pragma mark - updateRouterSSID


/**
当设备连接不上路由器（路由器断电或路由器名称、密码已被修改），WiFi&BLE设备（目前只支持云芯二代3.2.00模块）会通过蓝牙上报事件广播，uSDK收到此事件广播后，通过uSDKDevice的代理方法 -(void)device:(uSDKDevice *)device didUpdateFaultInformation:(uSDKFaultInformation *)faultInformation;持续上报，APP调用此接口可以修改设备连接的路由器或密码
 @param routerInfo  路由器信息的包装对象
        routerInfo.SSID 路由器的ssid，必选
        routerInfo.password 路由器的密码，必选
        routerInfo.BSSID 路由器的bssid，可选
        routerInfo.timeoutInterval 超时时间[30,120]
 @param progressNotify 更新进度通知
 @param completionHandler  error为nil时成功
 
 设备正在配置中。 ERR_USDK_DEVICE_CONFIG_IN_PROGRESS=-13006
 当前设备蓝牙未连接。ERR_USDK_DEVICE_BLE_IS_NOT_REACHABLE = -14059
 SSID长度不符合要求，规定(0,32]。STR_ERR_REASON_SSID_IS_INVALID
 密码长度不符合要求，规定[8,64]或是空。ERR_USDK_PASSWORD_LENGTH_INVALID
 BSSID不符合要求，规定为12个字符长度的16进制字符串，或是空。STR_ERR_REASON_BSSID_IS_INVALID
 超时参数不在有效值范围内，规定[30,120]。STR_ERR_REASON_TIMEOUT_IS_INVALID
 设备更新SSID PWD超时。ERR_USDK_UPDATE_SSID_PASSWORD_TIMEOUT = -14052
    无路由，路由断电等导致找不到路由器 -  USDK_ADV_STATE_CODE_CTL_NO_ROUTER 1001
    路由密码错误 - USDK_ADV_STATE_CODE_CTL_WRONG_PWD 1002
    配置信息疑似错误，疑似密码错误  -  USDK_ADV_STATE_CODE_CTL_SUSPECT_WRONG_PWD 1003
 设备更新SSID PWD失败，CAE直接返错。ERR_USDK_DEVICE_UPDATE_SSID_PASSWORD_CAE_ERROR = -14049
 @since 8.10.0
*/
- (void)updateRouterInfo:(uSDKRouterInfo *)routerInfo
               progressNotify:(void(^)(uSDKRouterInfoUpdateProgress updateProgress))progressNotify
            completionHandler:(void(^)(NSError *error))completionHandler;



#pragma mark - deprecated
/**
当设备连接不上路由器（路由器断电或路由器名称、密码已被修改），WiFi&BLE设备（目前只支持云芯二代3.2.00模块）会通过蓝牙上报事件广播，uSDK收到此事件广播后，通过uSDKDevice的代理方法 -(void)device:(uSDKDevice *)device didUpdateFaultInformation:(uSDKFaultInformation *)faultInformation;持续上报，APP调用此接口可以修改设备连接的路由器或密码
 @param ssid 路由器的ssid，必选
 @param password 路由器的密码，必选
 @param bssid 路由器的bssid，可选
 @param timeoutInterval 超时时间[30,120]
 @param progressNotify 更新进度通知
 @param success 成功回调
 @param failure 失败回调
 
 设备正在配置中。 ERR_USDK_DEVICE_CONFIG_IN_PROGRESS=-13006
 当前设备蓝牙未连接。ERR_USDK_DEVICE_BLE_IS_NOT_REACHABLE = -14059
 SSID长度不符合要求，规定(0,32]。STR_ERR_REASON_SSID_IS_INVALID
 密码长度不符合要求，规定[8,64]或是空。ERR_USDK_PASSWORD_LENGTH_INVALID
 BSSID不符合要求，规定为12个字符长度的16进制字符串，或是空。STR_ERR_REASON_BSSID_IS_INVALID
 超时参数不在有效值范围内，规定[30,120]。STR_ERR_REASON_TIMEOUT_IS_INVALID
 设备更新SSID PWD超时。ERR_USDK_UPDATE_SSID_PASSWORD_TIMEOUT = -14052
    无路由，路由断电等导致找不到路由器 -  USDK_ADV_STATE_CODE_CTL_NO_ROUTER 1001
    路由密码错误 - USDK_ADV_STATE_CODE_CTL_WRONG_PWD 1002
    配置信息疑似错误，疑似密码错误  -  USDK_ADV_STATE_CODE_CTL_SUSPECT_WRONG_PWD 1003
 设备更新SSID PWD失败，CAE直接返错。ERR_USDK_DEVICE_UPDATE_SSID_PASSWORD_CAE_ERROR = -14049
 @since 6.1.0
*/
- (void)updateRouterSSID:(NSString *)ssid
                password:(NSString *)password
                   bssid:(NSString *)bssid
         timeoutInterval:(NSTimeInterval)timeoutInterval
          progressNotify:(void(^)(uSDKRouterInfoUpdateProgress updateProgress))progressNotify
                 success:(void(^)(void))success
                 failure:(void(^)(NSError *error))failure DEPRECATED_ATTRIBUTE;

/**
 *  获取设备绑定信息
 *
 *  @param token 登录后云平台分配的token
 *  @param success 成功回调
 *  @param failure 失败回调
 *  @deprecated 5.2.1
 */
- (void)getDeviceBindInfoWithToken:(NSString *)token
                           success:(void(^)(NSString *info)) success
                           failure:(void(^)(NSError *error)) failure DEPRECATED_ATTRIBUTE;

/**
 获取设备绑定信息

 @param token 登录后云平台分配的token
 @param traceNodeCS 链式跟踪APP的CS节点
 @param success 成功回调
 @param failure 失败回调
 @deprecated 5.2.1
 */
- (void)getDeviceBindInfoWithToken:(NSString *)token
                     traceNodeCS:(uTraceNode *)traceNodeCS
                           success:(void(^)(NSString *info)) success
                           failure:(void(^)(NSError *error)) failure DEPRECATED_ATTRIBUTE;

/**
 *  获取设备绑定信息--同步方法
 *
 *  @param token 登录后云平台分配的token
 *  @param error 错误信息
 *  @return 设备绑定信息
 *  @deprecated 5.2.1
 */
- (NSString *)getDeviceBindInfoWithToken:(NSString *)token error:(NSError**)error DEPRECATED_ATTRIBUTE;


/**
 *  获取设备绑定信息 (此接口含有 重试策略，会在超时时间内 进行多次的重试)   uSDK4.5.01新增
 *
 *  @param token 登录后云平台分配的token
 *  @param timeoutInterval (超时时间设置范围20-120秒，推荐使用30秒)
 *  @param success 成功回调
 *  @param failure 失败回调
 *  @deprecated 5.2.1
 */
- (void)getDeviceBindInfoWithToken:(NSString *)token
                   timeoutInterval:(NSTimeInterval)timeoutInterval
                           success:(void(^)(NSString *info)) success
                           failure:(void(^)(NSError *error)) failure DEPRECATED_ATTRIBUTE;

/**
 获取设备绑定信息 (此接口含有 重试策略，会在超时时间内 进行多次的重试)   uSDK4.5.01新增

 @param token 登录后云平台分配的token
 @param timeoutInterval 超时时间(超时时间设置范围20-120秒,推荐使用30秒)
 @param traceNodeCS 关联的CS节点对象，如果APP有链式埋点，则需要APP将关联的CS节点对象传给uSDK
 @param success 成功回调
 @param failure 失败回调
 @deprecated 5.2.1
 */
- (void)getDeviceBindInfoWithToken:(NSString *)token
                   timeoutInterval:(NSTimeInterval)timeoutInterval
                       traceNodeCS:(uTraceNode*)traceNodeCS
                           success:(void(^)(NSString *info)) success
                           failure:(void(^)(NSError *error)) failure DEPRECATED_ATTRIBUTE;

/**
 获取设备绑定信息，无埋点

 @param token 登录后云平台分配的token
 @param timeoutInterval 超时时间
 @param error 错误信息
 @return 绑定信息
 @deprecated 5.2.1
 */
- (NSString *)getDevBindInfoWithToken:(NSString *)token timeoutInterval:(NSTimeInterval)timeoutInterval error:(NSError**)error DEPRECATED_ATTRIBUTE;


@end

/**
 *  uSDKDevice对象代理协议，用于接收回调消息（设备状态变化、属性变化、报警等）
 */
@protocol uSDKDeviceDelegate <NSObject>

@optional

/**
 *  设备基本信息变化（netType和ip）
 *
 *  @param device   信息变化的设备
 */
-(void)deviceDidUpdateBaseInfo:(uSDKDevice*)device;

/**
 *  设备连接状态变化, 状态变为就绪时，先上报状态变化，再上报属性变化
 *
 *  @param device   状态变化的设备
 *  @param state    变化后的状态
 *  @param error  离线原因
 */
-(void)device:(uSDKDevice*)device didUpdateState:(uSDKDeviceState)state error:(NSError*)error;


/// 设备在离线状态变化
/// @param device 状态变化的设备
/// @param onlineState 变化后的状态
/// @since 8.10.0
-(void)device:(uSDKDevice*)device didUpdateOnlineState:(uSDKDeviceOnlineState)onlineState;


/// 设备在离线状态变化v2
/// @param device 状态变化的设备
/// @param onlineStateV2 变化后的状态
/// @since 8.12.0
-(void)device:(uSDKDevice*)device didUpdateOnlineStateV2:(uSDKDeviceOnlineStateV2)onlineStateV2;

/**
 *  设备属性状态变化
 *
 *  @param device     设备对象
 *  @param attributes 属性发生变化的集合
 */
-(void)device:(uSDKDevice*)device didUpdateValueForAttributes:(NSArray<uSDKDeviceAttribute*>*)attributes ;


/**
 *  设备属性状态变化
 *
 *  @param device     设备对象
 *  @param attributes 属性发生变化的集合
 *  @param traceID    大循环通路链路SN
 *  @param timestamp  uSDK收到属性上报的时间戳，单位毫秒
 *  @since 10.8.0
 */
-(void)device:(uSDKDevice*)device didUpdateValueForAttributes:(NSArray<uSDKDeviceAttribute*>*)attributes uTraceID:(NSString *)traceID timestamp:(NSTimeInterval)timestamp;


/**
 *  设备报警列表
 *
 *  @param device 发生报警的设备对象
 *  @param alarms 报警信息列表，列表中数据类型为uSDKDeviceAlarm
 */
-(void)device:(uSDKDevice*)device didReceiveAlarms:(NSArray<uSDKDeviceAlarm*>*)alarms ;

/**
 *  子机设备列表变化
 *
 *  @param device           主机设备
 *  @param subDevices       新增的子机设备列表
 */
-(void)device:(uSDKDevice*)device didAddSubDevices:(NSArray<uSDKSubDevice*>*)subDevices;


/**
 接收资源数据

 @param device 设备对象
 @param resource 资源名称
 @param data 资源数据
 */
- (void)device:(uSDKDevice *)device didReceiveResource:(NSString *)resource data:(NSData *)data;

/**
 新的接收资源数据

 @param device 设备对象
 @param resource 资源名称
 @param JSONData 资源数据
 @since 8.5.0
 */
- (void)device:(uSDKDevice *)device didReceiveDecodeResource:(NSString *)resource data:(NSString *)JSONData;


/**
 蓝牙历史数据上报，通过uSDKDevice对象的代理上报，uSDK不做处理，仅透传
 如果历史数据为空，则会收到一次data == nil的上报
 如果历史数据部位空，则data不会为nil

 @param device 上报数据的设备对象
 @param currentCount 当前数据是第几条
 @param totalCount 总计数据条数
 @param data 透传的二进制数据
 @since 5.4.0
 */
- (void)device:(uSDKDevice *)device didReceiveBLEHistoryData:(NSData *)data currentCount:(NSUInteger)currentCount totalCount:(NSUInteger)totalCount;


/**
 蓝牙实时数据上报，通过uSDKDevice对象的代理上报，uSDK不做处理，仅透传

 @param device 上报数据的设备对象
 @param data 透传的二进制数据
 @since 5.4.0
 */
- (void)device:(uSDKDevice *)device didReceiveBLERealTimeData:(NSData *)data;

/**
 设备底板固件升级状态变化

 @param device 设备对象
 @param FOTAStatusInfo 设备底板固件升级状态信息
 @since 5.4.0
 */
- (void)device:(uSDKDevice *)device didUpdateBoardFOTAStatus:(uSDKFOTAStatusInfo *)FOTAStatusInfo;


/// 设备故障信息上报
/// @param device 设备对象
/// @param faultInformation 故障信息类
/// @since 6.1.0
- (void)device:(uSDKDevice *)device didUpdateFaultInformation:(uSDKFaultInformation *)faultInformation;

/**
 * 设备事件回调
 *
 * @param device 事件上报的设备对象
 * @param events 事件
 * @since 8.2.0
 */
- (void)device:(uSDKDevice *)device didReceiveEvent:(NSArray<uSDKDeviceEvent*>*)events;


/// 设备连接（订阅）失败回调
/// @param device 事件上报的设备对象
/// @param error 错误描述对象
///            回调值包括：ERR_USDK_DEVICE_IS_NOT_AUTHORIZED = - 14063  当前设备鉴权未通过
/// @since v8.10.0
- (void)device:(uSDKDevice *)device onConnectError:(NSError *)error;


/// 快连连接超时上报
/// @param device 超时连接的设备对象
/// @param timeoutType 超时类型
///            uSDKQCConnectTimeoutTypeConnect, 连接超时
///            uSDKQCConnectTimeoutTypeAuthorize, 鉴权超时
/// @since v8.15.0
- (void)device:(uSDKDevice *)device onQCConnectTimeout:(uSDKQCConnectTimeoutType)timeoutType;
/**
 * 设备休眠状态改变
 * @param device 状态改变的设备对象
 * @param sleepState 状态
 * @since v8.10.0 添加设备状态变化代理接口
 */
- (void)device:(uSDKDevice *)device didUpdateSleepState:(uSDKDeviceSleepState )sleepState;

/**
 * 设备主动离线原因变化
 * @param device 状态改变的设备对象
 * @param activeOfflineCause 离线原因
 * @since v9.12.0
 */
- (void)device:(uSDKDevice *)device didUpdateActiveOfflineCause:(uSDKDeviceActiveOfflineCause )activeOfflineCause;

/// 设备功能集变化
/// @param device 状态变化的设备
/// @param pid 变化后的属性值
/// @since 10.0.0
-(void)device:(uSDKDevice*)device didUpdatePid:(NSString *)pid;

/// 支持仅配置
/// @param device 状态变化的设备
/// @param upRouter 变化后的属性值
/// @since 10.0.0
-(void)device:(uSDKDevice*)device didUpdateUpRouter:(NSInteger)upRouter;

/// 离线天数变化
/// @param device 状态变化的设备
/// @param offlineDays 变化后的属性值
/// @since 10.0.0
-(void)device:(uSDKDevice*)device didUpdateOfflineDays:(NSInteger)offlineDays;

/// wifi在离线状态变化
/// @param device 状态变化的设备
/// @param wifiOnlineState 变化后的属性值
/// @since 10.0.0
-(void)device:(uSDKDevice*)device didUpdateWifiOnlineState:(uSDKDeviceOnlineState)wifiOnlineState;

/// 仅配置状态变化
/// @param device 状态变化的设备
/// @param onlyConfigState 变化后的属性值
/// @since 10.1.0
-(void)device:(uSDKDevice*)device didUpdateOnlyConfigState:(uSDKDeviceOnlyConfigState)onlyConfigState;

/**
 * 设备网络质量等级变化上报
 *
 * @param device 网络质量变化的设备对象
 * @param qualityLevel 网络质量等级
 * @since v8.12.0
 */
- (void)device:(uSDKDevice *)device didUpdateNetQualityLevel:(uSDKDeviceNetQualityLevel )qualityLevel;

/**
 * 设备FOTA升级状态上报
 *
 * @param device 升级进度更新的设备对象
 * @param statusInfo 升级进度
 * @since v8.16.0
 */
- (void)device:(uSDKDevice *_Nonnull)device didUpdateFOTAStatus:(uSDKFOTAStatusInfo *_Nonnull)statusInfo;


/// 设备本地通路连接状态变化上报
/// @param device 变化的设备对象
/// @param state 变化的本地通路状态
/// @param error 对应本地通路离线原因
- (void)device:(uSDKDevice *_Nonnull)device didUpdateLocalState:(uSDKDeviceState)state error:(NSError *)error;


/// 设备蓝牙通路连接状态变化上报
/// @param device 变化的设备对象
/// @param state 变化的蓝牙通路状态
/// @param error 对应蓝牙通路离线原因
- (void)device:(uSDKDevice *_Nonnull)device didUpdateBleState:(uSDKDeviceState)state error:(NSError *)error;

/// 设备远程通路连接状态变化上报
/// @param device 变化的设备对象
/// @param state 变化的远程通路状态
/// @param error  设备远程通路离线原因
- (void)device:(uSDKDevice *_Nonnull)device didUpdateRemoteState:(uSDKDeviceState)state error:(NSError *)error;


/// 设备配对状态变化上报
/// @param device 变化的设备对象
/// @param state 状态值，int类型
/// @since 10.4.0
- (void)device:(uSDKDevice *_Nonnull)device didUpdateUMeshPairState:(NSInteger)state;

/// 可配对设备列表上报
/// @param device 变化的设备对象
/// @param list 当前可配对设备列表，其中 devId 可能为空
/// @since 10.7.0
- (void)device:(uSDKDevice *_Nonnull)device didUpdateUMeshUnPairDevs:(NSArray <uSDKUMeshPairDeviceInfo *>*)list;

///已配对设备列表上报
/// @param device 变化的设备对象
/// @param list 当前已配对设备列表，其中 devId 可能为空
/// @since 10.7.0
- (void)device:(uSDKDevice *_Nonnull)device didUpdateUMeshPairedDevs:(NSArray <uSDKUMeshPairDeviceInfo *>*)list;
/// 设备连接过程错误码状态上报
/// @param device 变化的设备对象
/// @param code 连接过程错误码，int类型
/// @since 10.8.0
- (void)device:(uSDKDevice *_Nonnull)device didUpdateConnectErrorCode:(NSInteger)code;

@end


/**
 *    子设备类，包含子设备特有属性，能查看父设备，执行命令
 */
@interface uSDKSubDevice : uSDKDevice{
    NSString* _subId;
    __weak uSDKDevice* _parentDevice;
}

/**
 *	@brief	功能描述：<br>
 *      复杂类设备子机号；此变量只读，设置无效
 */
@property (nonatomic, strong,readonly) NSString* subId;

/**
 *	@brief	功能描述：<br>
 *      子设备所属父设备
 */
@property (nonatomic, weak,readonly) uSDKDevice* parentDevice;

-(instancetype)initWithID:(NSString*)subId parentDevice:(uSDKDevice*)parentDevice;

@end

/**
 * 快连连接选填参数
 */
@interface uSDKQCConnectInfo : NSObject
//是否获取属性。 默认值为YES
@property (nonatomic, assign) BOOL isGetAllProperty;
//流程方式。默认值为0。    0代表详情页快连流程，1代表快连优先配网流程，
@property (nonatomic, assign) NSInteger QCFlowType;
//快速初始化方法
-(instancetype)initWithisGetAllProperty:(BOOL)isGetAllProperty QCFlowType:(NSInteger)QCFlowType;

@end
