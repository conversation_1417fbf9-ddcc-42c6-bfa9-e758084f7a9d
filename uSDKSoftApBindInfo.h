//
//  uSDKSoftApBindInfo.h
//  uSDK
//
//  Created by wangbing on 2018/10/31.
//  Copyright © 2018 haier. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "uTrace.h"
#import "uSDKBaseBindInfo.h"

NS_ASSUME_NONNULL_BEGIN


/**
 softAp配置绑定信息类
 */
@interface uSDKSoftApBindInfo : uSDKBaseBindInfo
/**
 要配置到的SSID名称，必填, SSID最长支持32个字符
 */
@property (nonatomic, copy) NSString* ssid;

/**
 要配置的SSID对应的密码，必填，可以为空密码，如果有密码，则长度必须>=8 && <=64
 */
@property (nonatomic, copy) NSString* password;

/**
 要配置的路由器的MAC
 */
@property (nonatomic, copy) NSString* bssid;

/**
 设备SoftAp热点的SSID，必填
 
 如果在调用softap绑定接口时，手机连接的热点与该属性的值不匹配，则返回-13016(未连接到设备热点)错误
 
 @since 6.0.0
 */
@property (nonatomic, copy) NSString* iotDeviceSSID;

/**
 设备SoftAp热点的BSSID
 @deprecated 6.0.0 属性废弃后，不再校验该属性
 */
@property (nonatomic, copy) NSString* iotDevBssid DEPRECATED_ATTRIBUTE;

/**
 配置超时时间（单位是秒，范围为30秒-120秒），默认60秒
 */
@property (nonatomic, assign) NSTimeInterval timeoutInterval;

/**
 配置方式为非安全或安全，默认为非安全
 */
@property (nonatomic, assign) BOOL security;

/**
 当app进入后台时是否计算时间，默认为YES
 */
@property (nonatomic, assign) BOOL isTimerValidInBackground;

/**
 主网关域名
 */
@property (nonatomic, copy) NSString* mainGatewayDomain;

/**
 主网关端口
 */
@property (nonatomic, assign) NSInteger mainGatewayPort;

/**
 可取值 JP(日本) CN(中国) EU(欧洲) US(美国） WW(世界)
 */
@property (nonatomic, copy) NSString *country;

/**
 App用来进行链式跟踪的CS点，该字段为选填
 */
@property (nonatomic, strong) uTraceNode *traceNodeCS;

- (instancetype)initWithSSID:(NSString *)ssid
                    password:(NSString *)password
                       bssid:(NSString *)bssid NS_DESIGNATED_INITIALIZER;

- (BOOL)isValid:(NSError **)outError;
@end

NS_ASSUME_NONNULL_END
