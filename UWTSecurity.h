//
//  UWTSecurity.h
//  uSDK
//
//  Created by 郭永峰 on 2023/7/25.
//  Copyright © 2023 haier. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface UWTSecurity : NSObject

/// 计算MD5加密key
/// - Parameter data: 根据协议拼接的二进制数据
/// - Returns: 返回 key字符串
+ (NSString *)md5Hex:(NSData *)data;

/// 加密数据
/// - Parameters:
///   - key: 通过md5Hex 计算的key
///   - data: 要加密的数据
///  - Returns: 加密之后的数据
+ (NSData *)encryptWithKey:(NSString *)key data:(NSData *)data;

/// 解密数据
/// - Parameters:
///   - key: 通过md5Hex计算的key
///   - data: 要解密的数据
/// - Returns: 解密之后的数据
+ (NSData *)descryptWithKey:(NSString *)key data:(NSData *)data;

@end

NS_ASSUME_NONNULL_END
