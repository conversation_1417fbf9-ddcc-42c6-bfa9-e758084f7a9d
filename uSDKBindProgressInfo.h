//
//  uSDKBindProgressInfo.h
//  uSDK
//
//  Created by wangbing on 2019/1/2.
//  Copyright © 2019 haier. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "uSDKDeviceInfo.h"

NS_ASSUME_NONNULL_BEGIN
/**
 设备配网进度通知
 */
typedef NS_ENUM(NSUInteger, uSDKBindProgress) {
    /**
     开始连接设备
     */
    uSDKBindProgressConnectDevice,
    /**
     开始发送配置信息
     */
    uSDKBindProgressSendConfigInfo,
    /**
    一键配网配置设备失败
     */
    uSDKBindProgressConfigDeviceFailure,
    /**
     开始校验
     */
    uSDKBindProgressVerification,
    /**
     开始绑定设备
     */
    uSDKBindProgressBindDevice,
    /**
    一键配网绑定设备失败
     */
    uSDKBindProgressBindDeviceFailure,
    /**
     绑定设备成功
     */
    uSDKBindProgressBindDeviceSuccess,
    /**
     开始进入组网模式
     */
    uSDKBindProgressStartEnterNetworkingMode,
    /**
     开始组网
     */
    uSDKBindProgressStartNetworking,
    /**
     开始绑定子机
     */
    uSDKBindProgressStartBindSlaveDevice,

};


/**
 配置绑定接口过程上报
 */
@interface uSDKBindProgressInfo : NSObject
@property (nonatomic, copy) NSString *deviceID;
@property (nonatomic, copy) NSString *uplusID;
@property (nonatomic, assign) uSDKBindProgress bindProgress;

/**
 设备信息
 一键快连配网上报进度，会携带对应的deviceInfo对象。其他上报该对象为空
 @since v8.8.0
 */
@property (nonatomic, strong) uSDKDeviceInfo *deviceInfo;

@end


/// mesh批量绑定进度
@interface uSDKBLEMeshBindProgressInfo : NSObject

@property (nonatomic, strong) uSDKDeviceInfo *deviceInfo;
@property (nonatomic, strong) NSError *error;

@end

NS_ASSUME_NONNULL_END
