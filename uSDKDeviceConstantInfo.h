//
//  uSDKDeviceConstantInfo.h
//  uSDK
//
//  Created by 王兵 on 2021/9/1.
//  Copyright © 2021 haier. All rights reserved.
//

#ifndef uSDKDeviceConstantInfo_h
#define uSDKDeviceConstantInfo_h


/**
 设备仅配网状态
 @since 10.1.0
 */
typedef NS_ENUM(NSUInteger, uSDKDeviceOnlyConfigState) {
    /**
     表示不能配置
     */
    uSDKDeviceOnlyConfigStateUnConfigurable,
    /**
     表示靠近可配置
     */
    uSDKDeviceOnlyConfigStateNearConfigurable,
    /**
     表示可配置
     */
    uSDKDeviceOnlyConfigStateConfigurable,
};


/**
 设备在线状态
 @since 8.10.0
 */
typedef NS_ENUM(NSUInteger, uSDKDeviceOnlineState) {
    /**
     表示离线
     */
    uSDKDeviceOnlineStateOffline,
    /**
     表示在线
     */
    uSDKDeviceOnlineStateOnline,
};

/**
 设备融合在离线状态v2
 @since 8.12.0
 */
typedef NS_ENUM(NSUInteger, uSDKDeviceOnlineStateV2) {
    /**
     远程获取到设备为离线且本地搜索不到时，表示离线
     */
    uSDKDeviceOnlineStateV2Offline,
    /**
     远程获取到设备为在线或本地搜索到，但未就绪，表示在线不就绪
     */
    uSDKDeviceOnlineStateV2OnlineNotReady,
    /**
     远程获取到设备为在线或本地搜索到时且已就绪，表示在线且就绪
     */
    uSDKDeviceOnlineStateV2OnlineReady
};

/**
 * 设备休眠状态
 * @since v8.10.0  添加低功耗设备休眠状态
 */
typedef NS_ENUM(NSInteger,uSDKDeviceSleepState) {
    /**
     * 设备未休眠状态
     */
    uSDKDeviceSleepStateUnsleeping = 0,
    /**
     * 设备休眠中状态
     */
    uSDKDeviceSleepStateSleeping,
    /**
     * 设备唤醒中状态
     */
    uSDKDeviceSleepStateWakingUp,
    /**
     * 低功耗模式状态
     * @since 9.10.0
     */
    uSDKDeviceSleepStateLowPowerMode DEPRECATED_ATTRIBUTE,//9.12.0版本开始，该字段废弃
};


/**
 * 设备主动离线原因
 * @since v9.12.0
 */
typedef NS_ENUM(NSInteger,uSDKDeviceActiveOfflineCause) {
    /**
     * 设备无主动离线原因
     */
    uSDKDeviceActiveOfflineCauseNULL = 0,
    /**
     * 设备普通离线
     */
    uSDKDeviceActiveOfflineCauseNormal = 1,
    /**
     * 设备进入低功耗离线
     */
    uSDKDeviceActiveOfflineCauseLowPowerMode = 2,
    /**
     * 设备关闭wifi离线
     */
    uSDKDeviceActiveOfflineCauseCloseWIFI = 3,
};


/**
 * 设备网络质量等级
 *
 * @since v8.12.0 添加设备网络质量等级枚举
 */

typedef NS_ENUM(NSInteger, uSDKDeviceNetQualityLevel) {
    /**
     * 设备网络质量未知
     */
    uSDKDeviceNetQualityLevelUnKnown = 0,
    /**
     * 设备网络质量优
     */
    uSDKDeviceNetQualityLevelExcellent,
    /**
     * 设备网络质量良
     */
    uSDKDeviceNetQualityLevelGood,
    /**
     * 设备网络质量合格
     */
    uSDKDeviceNetQualityLevelQualified,
    
    /**
     * 设备网络质量差
     */
    uSDKDeviceNetQualityLevelPoor,
};

/**
 * 快连设备超时类型
 *
 * @since v8.13.0 添加快连接口超时类型
 */

typedef NS_ENUM(NSUInteger, uSDKQCConnectTimeoutType) {
    /**
     * 连接超时
     */
    uSDKQCConnectTimeoutTypeConnect,
    /**
     * 鉴权超时
     */
    uSDKQCConnectTimeoutTypeAuthorize,
};

/**
 * 设备FOTA通道
 */
typedef NS_ENUM(NSInteger, uSDKFOTAChannel) {
    
    uSDKFOTAChannelNULL = 0,
    
    uSDKFOTAChannelWiFi,
    
    uSDKFOTAChannelBLE,
};

/// 指定交互使用通路
/// @since 9.5.0
typedef NS_ENUM(NSUInteger, uSDKCommunicationChannel) {
    /*
     通路自动选择
     */
    uSDKCommunicationChannelAuto,
    /*
     使用本地通路
     */
    uSDKCommunicationChannelLocal,
    /*
     使用远程通路
     */
    uSDKCommunicationChannelRemote,
    /*
     使用普通蓝牙通路
     */
    uSDKCommunicationChannelBLE,
    /*
     使用蓝牙mesh通路
     */
    uSDKCommunicationChannelBLEMesh,
    /*
     使用蓝牙gateway通路
     */
    uSDKCommunicationChannelBLEMeshGateway
};

#endif /* uSDKDeviceConstantInfo_h */
