//
//  uSDKCellularDeviceBindInfo.h
//  uSDK
//
//  Created by like on 2021/6/8.
//  Copyright © 2021 haier. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "uTraceNode.h"
NS_ASSUME_NONNULL_BEGIN

/**
 蜂窝设备绑定信息类
 */
@interface uSDKCellularDeviceBindInfo : NSObject
/**
 设备模块id,目前仅支持imei码
 */
@property (nonatomic, copy) NSString *moduleID;
/**
 绑定超时时间（单位是秒，范围为30秒-180秒），默认90秒
 */
@property (nonatomic, assign) NSTimeInterval timeoutInterval;
/**
 App用来进行链式跟踪的CS点，该字段为选填
 */
@property (nonatomic, strong) uTraceNode *traceNodeCS;

@end

NS_ASSUME_NONNULL_END
