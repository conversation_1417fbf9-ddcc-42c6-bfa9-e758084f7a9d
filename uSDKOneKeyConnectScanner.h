//
//  uSDKOneKeyConnectScanner.h
//  uSDK
//
//  Created by 郭永峰 on 2021/6/17.
//  Copyright © 2021 haier. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "uSDKDeviceInfo.h"
#import "uSDKDevice.h"
NS_ASSUME_NONNULL_BEGIN

@protocol uSDKOneKeyConnectScannerDelegate;

/**
 一键快连扫描类
 
 该类用于发现路由器中待配置和待绑定的设备
 
 如果调用了扫描可配置设备接口`startScanConfigurableDeviceCompleteHandle:`，则可以通过实现`uSDKOneKeyConnectScannerDelegate`代理，来接收可配置设备的变化上报
      
 注：这里的scanner非单例对象，delegate不支持多代理功能，多个地方都需要处理代理方法时，可以创建多个对象调用扫描接口。
 
 @since v8.8.0
 */
@interface uSDKOneKeyConnectScanner : NSObject

/**
 扫描结果上报监听代理，会通过下面的个代理方法上报扫描结果
    - (void)oneKeyConnectScanner:(uSDKOneKeyConnectScanner*)scanner didFindNewDevice:(uSDKDeviceInfo *)device;
    - (void)oneKeyConnectScanner:(uSDKOneKeyConnectScanner*)scanner didRemoveDevice:(uSDKDeviceInfo *)device;
    - (void)oneKeyConnectScanner:(uSDKOneKeyConnectScanner*)scanner didUpdateDevice:(uSDKDeviceInfo *)device;
*/
@property (nonatomic, weak) id<uSDKOneKeyConnectScannerDelegate> delegate;

/**
 本地存储的可配置设备列表
 */
@property (nonatomic, strong, readonly) NSArray<uSDKDeviceInfo *> *configurableDeviceList;

/**
 初始化方法
 
 需要传入路由器对象来初始化对应的scanner
 
 @param device  路由器对象,不能为空，参数传空，则初始化失败
 @return 返回`uSDKOneKeyConnectScanner`实例
*/
- (instancetype)initWithDevice:(uSDKDevice *)device;

/**
 开启扫描可配置设备列表
  
 @param completeHandle  扫描结果回调，error 值为nil则开启扫描成功
*/
- (void)startScanConfigurableDeviceCompleteHandle:(void(^)(NSError * _Nullable))completeHandle;

/**
 停止扫描可配置设备列表的
*/
- (void)stopScanConfigurableDevice;

@end


/**
 一键快连代理协议
*/
@protocol uSDKOneKeyConnectScannerDelegate <NSObject>

@optional

/**
 发现新增的待入网设备

 @param scanner uSDKOneKeyConnectScanner 对象
 @param devices  增加的待入网设备对象
 */
- (void)oneKeyConnectScanner:(uSDKOneKeyConnectScanner*)scanner didFindNewDevices:(NSArray<uSDKDeviceInfo *> *)devices;

/**
 移除已发现的待入网设备
  
 @param scanner uSDKOneKeyConnectScanner 对象
 @param devices  移除的设备对象
 */
- (void)oneKeyConnectScanner:(uSDKOneKeyConnectScanner*)scanner didRemoveDevices:(NSArray<uSDKDeviceInfo *> *)devices;

/**
 已发现的待入网设备有属性更新

 @param scanner uSDKOneKeyConnectScanner 对象
 @param devices 更新的设备对象
 */
- (void)oneKeyConnectScanner:(uSDKOneKeyConnectScanner*)scanner didUpdateDevices:(NSArray<uSDKDeviceInfo *> *)devices;

@end


NS_ASSUME_NONNULL_END
