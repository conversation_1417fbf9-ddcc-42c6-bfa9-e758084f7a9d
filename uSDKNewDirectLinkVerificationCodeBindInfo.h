//
//  uSDKNewDirectLinkVerificationCodeBindInfo.h
//  uSDK
//
//  Created by 王兵 on 2020/4/27.
//  Copyright © 2020 haier. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "uSDKBaseBindInfo.h"

NS_ASSUME_NONNULL_BEGIN
/**
 新直连绑定验证码方式绑定信息
 @since 6.0.0
 */
@interface uSDKNewDirectLinkVerificationCodeBindInfo : uSDKBaseBindInfo
/**
 通过uSDKDeviceScanner扫描到的新直连设备对象，device.configType & uSDKDeviceConfigTypeNewDirectLink == 1表示该设备支持新直连配网
 @since 6.0.0
 */
@property (nonatomic, strong) uSDKDeviceInfo *deviceInfo;

/**
 超时时间（单位是秒，范围为30秒-180秒），默认90秒
 @since 6.0.0
 */
@property (nonatomic, assign) NSTimeInterval timeoutInterval;

/**
 打点需要的CS节点
 @since 6.0.0
 */
@property (nonatomic,strong) uTraceNode *traceNodeCS;

/**
 验证码
 
 验证码长度[6,63]
 @since 6.0.0
 */
@property (nonatomic, copy) NSString *verificationCode;
@end

NS_ASSUME_NONNULL_END
