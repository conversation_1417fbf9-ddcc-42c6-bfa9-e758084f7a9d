//
//  uSDKManager.h
//  uSDK_iOS_v2
//
//  Created by <PERSON><PERSON> on 14-1-7.
//  Copyright (c) 2014年 haierubic. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "uSDKConstantInfo.h"
#import "uSDKStartOptions.h"

#define USDK_VERSION @"10.8.0"
#define USDK_BUILD_DATE @"20250407105452"

@protocol uSDKManagerDelegate;

/**
 *  uSDK管理者类，用于管理uSDK及监听uSDK消息。
 */
@interface uSDKManager : NSObject{
}

/**
 *  代理，包括Session异常、业务消息。
 */
@property (nonatomic, weak) id<uSDKManagerDelegate> delegate;

/**
 *  uSDK版本号。
 */
@property (nonatomic, strong, readonly) NSString* uSDKVersion;

/**
 *  获取uSDK运行状态
 */
@property (nonatomic, assign, readonly) uSDKState state;

/**
 *  当前日志级别
 */
@property (nonatomic, assign, readonly) uSDKLogLevelConst logLevel;

/**
 *  获取uSDK单例，此单例用于管理uSDK服务。
 *
 *  @return 返回uSDKManager单例
 */
+ (uSDKManager*)defaultManager;


/**
 uSDK启动接口
 @discussion
    1.uSDK启动成功后，才可以使用其他API。执行结果会在Block中返回
    2.本接口包含了设置下载配置文件地址的功能，接口`setProfileServiceUrl:`已废弃
    3.本接口包含了设置HTTPDNS的功能，接口`enableFeatures:`已废弃
 
 @param startOptions uSDK启动参数对象
 @param success 启动成功后的block
 @param failure 启动失败后的block
 @since 5.4.1
 */
- (void)startSDKWithOptions:(uSDKStartOptions *)startOptions
                    success:(void(^)(void)) success
                    failure:(void(^)(NSError *error)) failure;


/**
 *  设置uSDK日志级别。uSDK的日志信息包括设置日志级别和是否写日志文件，日志级别设置的越低，输出的日志信息就越详细。
 *
 *  @param level         日志输出级别，详细含义请参见uSDKLogLevelConst
 *  @param isWriteToFile 否需要写日志文件。true表示把日志写到文件中，false表示把日志写到终端上
 *  @param success   调用成功后的block
 *  @param failure   调用失败后的block
 */
- (void)setLogWithLevel:(uSDKLogLevelConst)level
          isWriteToFile:(BOOL)isWriteToFile
                success:(void(^)(void))success
                failure:(void(^)(NSError *error)) failure;

/**
 *  停止uSDK。uSDK停止后，所有API将不可使用。
 *
 *    @return    返回停止uSDK结果
 */
- (uSDKErrorConst)stopSDK;

/**
 *  停止uSDK。uSDK停止后，所有API将不可使用。
 *
 *  @param success 停止成功回调
 *  @param failure 停止失败回调
 */
- (void)stopSDKWithSuccess:(void(^)(void))success
                   failure:(void(^)(NSError *error)) failure;

/**
 *  获取设备唯一标识。
 *
 *	@return	设备唯一标识
 */
-(NSString *)getClientID;


/**
 设置clientID, 如果app进行设置，则使用app的clientID进行绑定，如果app不设置，则使用SDK本身生成的clientID进行绑定

 @param clientID 移动终端唯一标识
 */
-(void)setClientID:(NSString *)clientID;

/**
 添加代理监听
 @param delegate 代理对象
 @since v8.8.0  添加一对多代理支持
 */
- (void)addDelegate:(id<uSDKManagerDelegate>)delegate;
/**
 移除代理监听
 @param delegate 代理对象
 @since v8.8.0  添加一对多代理支持
 */
- (void)removeDelegate:(id<uSDKManagerDelegate>)delegate;
/**
 判断是否包含
 @param delegate 代理对象
 @since v8.8.0  添加一对多代理支持
 */
- (BOOL)containsDelegate:(id<uSDKManagerDelegate>)delegate;


#pragma mark - deprecated
/**
 *  设置下载配置文件服务器地址。本地址需要先设置成功后，才能启动uSDK。若不设置本地址直接启动uSDK，默认为国内标准地址，此方法为同步方法。
 *
 *  @param url 下载配置文件服务器的地址，参数不能为@""或nil，长度不超过128，且必须以http或者https开头.
 *
 *  @return 返回设置成功或者失败
 *  @deprecated 5.4.1
 */

- (uSDKErrorConst)setProfileServiceUrl:(NSString *)url DEPRECATED_ATTRIBUTE;


/**
 特性开关（如是否启用链式跟踪,是否启用DNS解析,是否启用HTTPDNS解析）

 @param features 特性
 @deprecated 5.4.1 方法已成空实现，调用该接口不会产生任何效果
 */
- (void)enableFeatures:(uSDKFeatures)features DEPRECATED_ATTRIBUTE;

/**
 *  启动uSDK。uSDK启动后，才可以使用本SDK中的API。执行结果会在回调Block中返回。此方法为异步方法。
 *
 *  @param appID     开发者网站上申请获得的appid
 *  @param appKey     开发者网站上申请获得的appKey
 *  @param secretKey 开发者网站上申请获得的secretKey
 *  @param success   启动成功后的block
 *  @param failure   启动失败后的block
 *  @deprecated 5.4.1
 */
-(void)startSDKWithAppId:(NSString*) appID
                  appKey:(NSString*) appKey
               secretKey:(NSString*) secretKey
                 success:(void(^)(void)) success
                 failure:(void(^)(NSError *error)) failure DEPRECATED_ATTRIBUTE;



/**
 *  打开网络环境取数功能。
 *
 *    @return  成功 返回nil， 失败 返回具体的error信息。
 *    @since v9.13.0
 */
- (NSError *)enableNetEnv;

@end



/**
 *  uSDKManager对象的代理协议，包括Session异常、业务消息。
 */
@protocol uSDKManagerDelegate <NSObject>

@optional

/**
 *  会话异常通知。
 *
 *  @param sdkManager     当前uSDKManager对象
 *  @param token          当前会话失效的token
 */
-(void)uSDKManager:(uSDKManager*)sdkManager sessionException:(NSString*)token;

/**
 *  业务消息通知。
 *
 *  @param sdkManager      当前uSDKManager对象
 *  @param businessMessage 当前推送的业务消息
 */
-(void)uSDKManager:(uSDKManager*)sdkManager businessMessage:(NSString*) businessMessage;

@end


