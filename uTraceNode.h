//
//  uTraceNode.h
//  uSDKGeneral
//
//  Created by liugn on 2017/8/3.
//  Copyright © 2017年 beijinghaier. All rights reserved.
//

#import <Foundation/Foundation.h>

//模块安全类型0-表示非安全 1表示安全 2表示0.5安全 3：高级安全
#define uTrace_stype @"stype"
//配置方式:1安全 2非安全
#define uTrace_ctype @"ctype"
//配置协议：1代表smartlink 2代表softap 3代表ble
#define uTrace_cfg @"cfg"
//smartlink配置成功的方式：0代表组播 1代表广播
#define uTrace_scm @"scm"
//网器mac  dic
#define uTrace_dId @"dId"
//用户id dic
#define uTrace_uId @"uId"
//请求Header dic
#define uTrace_rqHd @"rqHd"
//请求BODY dic
#define uTrace_ipm @"ipm"
//响应内容  dic
#define uTrace_rrt @"rrt"
//异常信息
#define uTrace_exp @"exp"
//CR、SS阶段非00000时保存info信息，DI阶段可填写比较关注或重要的信息
#define uTrace_desc  @"desc"

/**
 *  跟踪节点类型
 */
typedef NS_ENUM(NSInteger, uTraceNodeType){
    /**
     *  Client Send（CS）: 客户端发起请求时埋点
     */
    uTraceNodeTypeCS,
    /**
     *  Client Receive（CR）： 客户端接收请求时埋点
     */
    uTraceNodeTypeCR,
    /**
     *  Server Receive（SR）： 服务端接收请求时埋点
     */
    uTraceNodeTypeSR,
    /**
     *  Server Send（SS） : 服务端将请求处理结果发送给客户端时埋点
     */
    uTraceNodeTypeSS,
    /**
     *  业务端自定义的日志埋点
     */
    uTraceNodeTypeDI
};


/**
 跟踪节点类
 */
@interface uTraceNode : NSObject {
    NSString *_uTraceId;
    NSString *_uSpanId;
}

/**
 CR/SR node 关联的 CS Node
 */
@property (nonatomic,strong) uTraceNode *relatedCSNode;
/**
 SDK的节点中当前CS节点相关联的SR节点
 */
@property (nonatomic,strong) uTraceNode *relatedSRNode;
/**
 扩展信息
 */
@property (nonatomic,strong) NSMutableDictionary *extendInfo;
/**
 链式跟踪ID
 */
@property (nonatomic,copy) NSString *uTraceId;
/**
 步骤ID
 */
@property (nonatomic,copy) NSString *uSpanId;
/**
 子服务名称
 */
@property (nonatomic,copy) NSString *subSys;
/**
 返回码
 */
@property (nonatomic,copy) NSString *code;
/**
 接口名称或URL
 */
@property (nonatomic,copy) NSString *bName;
/**
 请求技术方式
 */
@property (nonatomic,copy) NSString *prot;
/**
 处理耗时，CR-CS或SS-SR的时间戳。--毫秒
 */
@property (nonatomic,assign) UInt32 span;
/**
 时间戳
 */
@property (nonatomic,assign) UInt64 ts;
/**
 云平台用户 Id
 */
@property (nonatomic,copy) NSString *uId;

/**
 初始化方法

 @param subSys 子系统名称
 @param bName 接口名称
 @param prot 请求技术方式
 @return 初始化成功的对象
 */
-(instancetype)initWithSubSys:(NSString*)subSys bName:(NSString*)bName prot:(NSString*)prot;


/**
 快速初始化方法

 @param bName 接口名称
 @param prot 请求技术方式
 @param deviceID 设备ID
 @param ipm 请求BODY
 @return uTraceNode的实例对象
 */
+(uTraceNode*)createNodeWithBName:(NSString*)bName prot:(NSString*)prot deviceID:(NSString*)deviceID ipm:(NSDictionary*)ipm;

/**
 快速初始化方法，prot为local

 @param bName 接口名称
 @param deviceID 设备ID
 @param ipm 请求BODY
 @return uTraceNode的实例对象
 */
+ (instancetype)nodeLocalProtWithBName:(NSString*)bName deviceID:(NSString*)deviceID ipm:(NSDictionary*)ipm;
@end
