//
//  uSDKBindInfo.h
//  uSDK
//
//  Created by 赵睿 on 2019/6/5.
//  Copyright © 2019 haier. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "uSDKBaseBindInfo.h"


NS_ASSUME_NONNULL_BEGIN

/**
 摄像头扫码绑定信息类
 */
@interface uSDKCameraScanQRCodeBindInfo : uSDKBaseBindInfo


/**
 成品编码，必填
 */
@property (nonatomic, copy) NSString *productCode;

/**
 要配置到的SSID名称，必填, SSID最长支持32个字符
 */
@property (nonatomic, copy) NSString *ssid;

/**
 要配置的SSID对应的密码，必填，可以为空密码，如果有密码，则长度必须>=8 && <=64
 */
@property (nonatomic, copy) NSString *password;

/**
 超时时间
 */
@property (nonatomic,assign) NSTimeInterval timeoutInterval;

/**
 设备的uplusID 必填
 */
@property (nonatomic, copy) NSString *uplusID;

/**
 选填
 */
@property (nonatomic, copy) NSString *bssid;

/**
 选填 QRImageSize，默认值为311
 */
@property (nonatomic, assign) NSInteger qRCodeImgSize;

/**
 选填 默认值为 png
 */
@property (nonatomic, copy) NSString *mediaType;

/**
 选填 链式追踪标识
 */
@property (nonatomic, strong) uTraceNode *traceNodeCS;


/**
 选填  sn值为uTraceId值，拿不到时可不传。为空字串、null或空格时参数会被忽略，不在二维码中体现
 */
@property (nonatomic, copy) NSString *sn;


@end

NS_ASSUME_NONNULL_END
