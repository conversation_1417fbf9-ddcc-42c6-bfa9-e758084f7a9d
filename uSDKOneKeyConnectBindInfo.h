//
//  uSDKOneKeyConnectBindInfo.h
//  uSDK
//
//  Created by 郭永峰 on 2021/6/21.
//  Copyright © 2021 haier. All rights reserved.
//

#import <Foundation/Foundation.h>
@class uTraceNode;
#import "uSDKBaseBindInfo.h"

NS_ASSUME_NONNULL_BEGIN

/**
 一键快连绑定信息类

 @since v8.8.0
 */
@interface uSDKOneKeyConnectBindInfo : uSDKBaseBindInfo

/**
 支持一键配网的路由器设备
 */
@property (nonatomic, strong) uSDKDevice * routerDevice;

/**
 需要一键配置的设备列表
 */
@property (nonatomic, strong) NSArray<uSDKDeviceInfo *> *devices;

/**
 打点需要的CS节点
 */
@property (nonatomic,strong) uTraceNode *traceNodeCS;
/**
 超时时间, 建议30-180s，默认90s
 */
@property (nonatomic,assign) NSTimeInterval timeoutInterval;

@end

NS_ASSUME_NONNULL_END
