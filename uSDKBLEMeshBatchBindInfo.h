//
//  uSDKBLEMeshBatchBindInfo.h
//  uSDK
//
//  Created by like on 2023/2/28.
//

#import "uSDKBaseBindInfo.h"

NS_ASSUME_NONNULL_BEGIN
/**
 mesh批量绑定信息类
 */
@interface uSDKBLEMeshBatchBindInfo : uSDKBaseBindInfo

/**
 设备数组，其中每个设备应该是BLEMESH配网方式
 */
@property (nonatomic, strong) NSArray<uSDKDeviceInfo*> *devices;
/**
 超时时间（单位是秒，范围为30秒-180秒）
 */
@property (nonatomic, assign) NSTimeInterval timeoutInterval;

/**
 打点需要的CS节点
 */
@property (nonatomic,strong) uTraceNode *traceNodeCS;

@end

NS_ASSUME_NONNULL_END
