//
//  uSDKDeviceEvent.h
//  uSDK
//
//  Created by 赵睿 on 2020/12/14.
//  Copyright © 2020 haier. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "uSDKDeviceAttribute.h"
NS_ASSUME_NONNULL_BEGIN

/**
 * uSDK设备事件信息类，用于描述设备的事件消息。
 * @since 8.2.0
 */
@interface uSDKDeviceEvent : NSObject

/**
 * 事件名称-标准定义
 */
@property (nonatomic, copy, readonly) NSString *name;

/**
 * 事件类型[ message 消息;alarm 报警;fault 故障;]
 */
@property (nonatomic, copy, readonly) NSString *type;

/**
 * 该事件附带的属性列表
 */
@property (nonatomic, strong) NSArray<uSDKDeviceAttribute*> *attrs;



@end

NS_ASSUME_NONNULL_END
