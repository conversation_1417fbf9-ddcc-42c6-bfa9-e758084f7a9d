//
//  uSDKBLEMeshNetManager.h
//  uSDK
//
//  Created by like on 2023/2/27.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

/// BLEMesh网络信息管理类
@interface uSDKBLEMeshNetManager : NSObject

//mesh网关的TTL
@property (nonatomic, assign, readonly) NSInteger TTL;


/// 单例对象
+ (uSDKBLEMeshNetManager*)shareduSDKBLEMeshNetManager;

/// 设置mesh网络的TTL值
/// @param ttl ttl值
/// @param success 成功回调
/// @param failure 失败回调
-(void)setBLEMeshTTL:(NSInteger)ttl
             success:(void(^)(void))success
             failure:(void(^)(NSError *error))failure;



@end

NS_ASSUME_NONNULL_END
