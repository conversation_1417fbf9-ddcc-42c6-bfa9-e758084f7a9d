//
//  uSDKNetworkQualityInfo.h
//  uSDK
//
//  Created by 李可 on 2018/10/31.
//  Copyright © 2018年 haier. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN


/**
 设备网络质量信息类
 */
@interface uSDKNetworkQualityInfo : NSObject

/**
 设备信号强度。取值范围:1~100,无信号时值为 0。
 */
@property (nonatomic,assign) NSInteger signalStrength;

/**
 local Round Trip Time, 本地网络往返时间,即设备 ping 局域网路由器到收到应答消息耗时,单位毫秒。
 */
@property (nonatomic,assign) NSInteger localRoundTripTime;

/**
 local Packet Loss Rate,本地丢包率,即设备 4 次 ping 局域网路由器的丢包数。取值范围:0~4
 */
@property (nonatomic,assign) NSInteger localPacketLossRate;

/**
 remote Round Trip Time, 远程网络往返时间,即设备 ping 设备接入网关到收到应答消息耗时,或向设备发送心跳到收到心跳应答耗时,单位毫秒。
 */
@property (nonatomic,assign) NSInteger remoteRoundTripTime;

/**
 remote Packet Loss Rate,远程丢包率,即设备 ping 设备接入网关或向设备网关发送心跳是否丢包,可取值: 0 – 丢包;1 – 正常;
 */
@property (nonatomic,assign) NSInteger remotePacketLossRate;



@end

NS_ASSUME_NONNULL_END
