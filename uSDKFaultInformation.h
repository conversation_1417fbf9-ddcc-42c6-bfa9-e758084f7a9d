//
//  uSDKFaultInformation.h
//  uSDK
//
//  Created by 李可 on 2020/6/9.
//  Copyright © 2020 haier. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

/*
故障信息中
 
状态分类取值范围： 1，可控制。 2，配置中。 3，可触发。 4，已配置。 5，未绑定。
 
故障码取值范围：   1000，正常。
                1001，无路由，路由断电等导致找不到路由器。
                1002，路由密码错误，用户修改路由信息导致无法连上路由。
                1003，配置信息疑似错误，疑似密码错误。
                1004，设备绑定但未配置WiFi信息，设备未配置WiFi信息。
*/

/// 设备故障信息
@interface uSDKFaultInformation : NSObject

/// 状态分类
@property (nonatomic, assign) NSInteger state;

/// 故障码
@property (nonatomic, assign) NSInteger stateCode;

@end

NS_ASSUME_NONNULL_END
