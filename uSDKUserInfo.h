//
//  uSDKUserInfo.h
//  uSDK
//
//  Created by wangbing on 2019/7/15.
//  Copyright © 2019 haier. All rights reserved.
//

#import <Foundation/Foundation.h>
NS_ASSUME_NONNULL_BEGIN

/**
 用户信息类，用于同步App的用户信息到uSDK
 @since 5.4.1
 */
@interface uSDKUserInfo : NSObject
/**
 App登录后获取到的userId, 必填
 */
@property (nonatomic, copy) NSString *userID;

/**
 App登录后获取到的token, 必填
 */
@property (nonatomic, copy) NSString *userToken;

@end


/**
 用来快速构建uSDKUserInfo类
 */
@interface uSDKUserInfoBuilder : NSObject
@property (nonatomic, strong, readonly) uSDKUserInfo *userInfo;

@property (nonatomic, copy) uSDKUserInfoBuilder *(^userID) (NSString *userID);
@property (nonatomic, copy) uSDKUserInfoBuilder *(^userToken) (NSString *userToken);
@end

NS_ASSUME_NONNULL_END
