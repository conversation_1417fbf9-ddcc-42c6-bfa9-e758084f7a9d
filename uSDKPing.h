//
//  uSDKPing.h
//  uSDKGeneral
//
//  Created by liugn on 2017/11/21.
//  Copyright © 2017年 beijinghaier. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "uSDKPingResult.h"

/**
 测试网络状态
 */
@interface uSDKPing : NSObject

/**
 测试网络状态  uSDK4.4.02新增功能

 @param URLArray 要测试的目标URL
 @param results 测试成功后的测试结果
 @param failure 测试失败
 */
-(void)pingWithURLs:(NSArray<NSString*>*)URLArray
            results:(void(^)(NSArray<uSDKPingResult*>*)) results
            failure:(void(^)(NSError *error)) failure;

- (void)pingWithURLs:(NSArray<NSString*>*)URLArray package:(NSData *)data count:(NSInteger)count timeoutInterval:(NSTimeInterval)timeoutInterval results:(void(^)(NSArray<uSDKPingResult*>* results)) results
            failure:(void(^)(NSError *error)) failure;

/**
 中断测评
 */
-(void)stopPing;

@end
