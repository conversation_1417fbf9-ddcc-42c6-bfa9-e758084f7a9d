//
//  uSDKStartOptions.h
//  uSDK
//
//  Created by wangbing on 2019/7/13.
//  Copyright © 2019 haier. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "uSDKConstantInfo.h"

NS_ASSUME_NONNULL_BEGIN
@class uSDKIDCAreaCustomData;

/**
 uSDK的启动参数
 @since 5.4.1
 */
@interface uSDKStartOptions : NSObject

/**
 开发者网站上申请获得的appID, 必填
 */
@property (nonatomic, copy) NSString *appID;

/**
 开发者网站上申请获得的appKey, 必填
 */
@property (nonatomic, copy) NSString *appKey;

/**
 开发者网站上申请获得的secretKey, 必填
 */
@property (nonatomic, copy) NSString *secretKey;

/**
 数据中心，默认为uSDKIDCAreaChina
 对本参数进行设置时，如果area != uSDKIDCAreaChina，会将features特性全部关闭
 */
@property (nonatomic, assign) uSDKIDCArea area;

/**
 特性开关（如是否启用链式跟踪,是否启用DNS解析）
 默认不开启任何特性，需要App设置features启用相应功能
 注意：国外版本，如果开启features会导致SDK启动失败，返回-12006(不支持该特性)
 */
@property (nonatomic, assign) uSDKFeatures features;

/**
 默认为YES，表示开启BLE可控设备搜索，如果不想开启，则将该属性置为NO
 @since 6.0.0
 */
@property (nonatomic, assign) BOOL enableBLEControllableSearch;

/**
 默认为YES，表示开启wifi小循环设备搜索，如果不想开启，则将该属性置为NO
 @since 9.13.0
 */
@property (nonatomic, assign) BOOL enableWifiSearch;

/**
 默认为NO，表示不会加载本地缓存设备列表，如果想使用缓存列表，则将该属性置为YES
 @since 9.15.0
 */
@property (nonatomic, assign) BOOL useDevListLocalCache;

/**
 *  默认为零，ToC用户；toB企业用户为1；其他值无效
 *  @since8.16.0
 */
@property (nonatomic, assign) NSInteger businessType;

/**
 *  自定义数据中心，单独设置无效。需要配合area中的枚举值:uSDKIDCAreaCustom  一起使用。
 *  @since 10.2.0
 */
@property (nonatomic, strong) uSDKIDCAreaCustomData *areaCustomData;

/**
 默认为YES，表示是否启动自动配网功能
 @since 10.7.1
 */
@property (nonatomic, assign) BOOL enableAutoUpdatePassword;


/**
 * 用于设置回调的队列，默认为主队列
 * 目前会在以下几个地方使用该队列：
 *  1. startSDK
 *  2. uSDKDeviceDelegate 相关回调
 *
 * @since 10.5.1
 */
@property (nonatomic, strong) dispatch_queue_t callback_queue;

@end


/**
 用来快速构建uSDKStartOptions类
 */
@interface uSDKStartOptionsBuilder : NSObject
@property (nonatomic, strong, readonly) uSDKStartOptions *startOptions;

@property (nonatomic, copy) uSDKStartOptionsBuilder *(^appID) (NSString *appID);
@property (nonatomic, copy) uSDKStartOptionsBuilder *(^appKey) (NSString *appKey);
@property (nonatomic, copy) uSDKStartOptionsBuilder *(^secretKey) (NSString *secretKey);
@property (nonatomic, copy) uSDKStartOptionsBuilder *(^area) (uSDKIDCArea area);
@property (nonatomic, copy) uSDKStartOptionsBuilder *(^features) (uSDKFeatures features);
@property (nonatomic, copy) uSDKStartOptionsBuilder *(^enableBLEControllableSearch) (BOOL enableBLEControllableSearch);
@property (nonatomic, copy) uSDKStartOptionsBuilder *(^businessType) (NSInteger businessType);
@end

/**
 uSDK自定义数据中心参数
 @since 10.2.0
 */
@interface uSDKIDCAreaCustomData : NSObject
/**
 模块设备主网关域名
 */
@property (nonatomic, copy) NSString *uplugDomain;
/**
 模块设备主网关端口
 */
@property (nonatomic, assign) NSUInteger uplugPort;
/**
 ugw设备主网关域名
 */
@property (nonatomic, copy) NSString *ugwDomain;
/**
 ugw设备主网关端口
 */
@property (nonatomic, assign) NSUInteger ugwPort;
/**
 用户主网关域名
 */
@property (nonatomic, copy) NSString *userDomain;
/**
 用户主网关端口
 */
@property (nonatomic, assign) NSUInteger userPort;
/**
 uws服务域名
 */
@property (nonatomic, copy) NSString *uwsDomain;
/**
 配置文件下载地址
 */
@property (nonatomic, copy) NSString *profileURL;
/**
 行为统计服务器地址
 */
@property (nonatomic, copy) NSString *analyticsURL;
/**
 haigeek 服务域名
 */
@property (nonatomic, copy) NSString *haigeekDomain;
/**
 国家
 */
@property (nonatomic, copy) NSString *country;

@end

NS_ASSUME_NONNULL_END
