//
//  uSDKSmartLinkBindInfo.h
//  uSDK
//
//  Created by wangbing on 2018/12/27.
//  Copyright © 2018 haier. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "uTrace.h"
#import "uSDKBaseBindInfo.h"


NS_ASSUME_NONNULL_BEGIN

/**
 smartLink配置绑定信息类
 */
@interface uSDKSmartLinkBindInfo : uSDKBaseBindInfo
/**
 接入点WIFI名称，必填
 */
@property (nonatomic, copy) NSString *ssid;

/**
 接入点WIFI密码，支持无密码的情况，但如果WIFI有密码，则密码长度必须在[8,64]之间
 */
@property (nonatomic, copy) NSString *password;

/**
 超时时间（单位是秒，范围为30秒-180秒），默认90秒
 */
@property (nonatomic, assign) NSTimeInterval timeoutInterval;

/**
 配置方式为非安全或安全，默认为非安全
 */
@property (nonatomic, assign) BOOL security;

/**
 设备类型列表，当列表不为空时，会判断当前配置的设备类型是否在该列表中，如果不存在则配置失败。
 */
@property (nonatomic, strong) NSArray<NSString *>* uplusIDs;

/**
 设备ID
 */
@property (nonatomic, copy) NSString *deviceID;

/**
 App用来进行链式跟踪的CS点，该字段为选填
 */
@property (nonatomic, strong) uTraceNode *traceNodeCS;


- (instancetype)initWithSSID:(NSString *)ssid
                    password:(NSString *)password NS_DESIGNATED_INITIALIZER;

- (BOOL)isValid:(NSError **)outError;
@end

NS_ASSUME_NONNULL_END
