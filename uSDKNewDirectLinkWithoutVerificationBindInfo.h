//
//  uSDKNewDirectLinkWithoutVerificationBindInfo.h
//  uSDK
//
//  Created by like on 2021/11/19.
//  Copyright © 2021 haier. All rights reserved.
//

#import <uSDK/uSDK.h>
#import <Foundation/Foundation.h>
#import "uSDKDeviceInfo.h"
#import "uSDKBaseBindInfo.h"

NS_ASSUME_NONNULL_BEGIN
/**
 直连\非直连子机无验证方式绑定信息
 @since 8.12.0
 */
@interface uSDKNewDirectLinkWithoutVerificationBindInfo : uSDKBaseBindInfo
/**
 通过uSDKDeviceScanner扫描到的新直连设备对象，device.configType & uSDKDeviceConfigTypeNewDirectLink == 1表示该设备支持新直连配网
 @since 8.12.0
 */
@property (nonatomic, strong) uSDKDeviceInfo *deviceInfo;

/**
 超时时间（单位是秒，范围为30秒-180秒），默认90秒
 @since 8.12.0
 */
@property (nonatomic, assign) NSTimeInterval timeoutInterval;

/**
 打点需要的CS节点
 @since 8.12.0
 */
@property (nonatomic,strong) uTraceNode *traceNodeCS;


@end

NS_ASSUME_NONNULL_END
