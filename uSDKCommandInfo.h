//
//  uSDKCommandInfo.h
//  uSDK
//
//  Created by 郭永峰 on 2022/11/29.
//

#import <Foundation/Foundation.h>
#import "uTrace.h"
#import "uSDKDeviceConstantInfo.h"
#import "uSDKArgument.h"

NS_ASSUME_NONNULL_BEGIN

@interface uSDKCommandInfo : NSObject

/// 链式跟踪打点的trace
@property (nonatomic,strong) uTrace * trace;

/// 超时时间（5-120秒）
@property (nonatomic,assign) NSTimeInterval timeoutInterval;

/// 交互通路类型
@property (nonatomic,assign) uSDKCommunicationChannel communicationChannel;

@end

@interface uSDKWriteCommand : uSDKCommandInfo

/// 写入属性的名称
@property (nonatomic,copy) NSString * name;

/// 写入属性对应的value
@property (nonatomic,copy) NSString * value;

@end

@interface uSDKReadCommand : uSDKCommandInfo

/// 要读取的属性的名称
@property (nonatomic,copy) NSString * name;

@end

@interface uSDKOperationCommand : uSDKCommandInfo


/// 要执行的设备操作命令
@property (nonatomic,copy) NSString * operationName;

/// 要执行的设备操作命令列表
@property (nonatomic,strong) NSArray<uSDKArgument*>* args;

@end


NS_ASSUME_NONNULL_END
