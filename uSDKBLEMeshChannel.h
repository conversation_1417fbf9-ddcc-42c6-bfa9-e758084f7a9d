//
//  uSDKBLEMeshChannel.h
//  uSDK
//
//  Created by like on 2023/2/23.
//

#import <Foundation/Foundation.h>


/// 设备BLEMesh能力
@interface uSDKBLEMeshChannel : NSObject

/// 初始化
/// @param uSDKDevice 当前设备对象
-(instancetype)initWithuSDKDevice:(uSDKDevice*)uSDKDevice;

#pragma mark - GATT Proxy
//GATT Proxy state
typedef enum : uint8_t {
    uSDKMeshProxyFeatureState_supportedButDisabled,
    uSDKMeshProxyFeatureState_supportedAndEnabled,
    uSDKMeshProxyFeatureState_notSupported,
} uSDKMeshProxyFeatureState;

/// 获取当前设备的GATT Proxy state.
/// @param completionHandler 结果回调
- (void)getGATTProxyStateCompletion:(void(^_Nullable)(BOOL success, NSError *_Nullable error, uSDKMeshProxyFeatureState proxyState))completionHandler;

/// 设置当前设备的GATT Proxy state.
/// @param state GATT Proxy state.
/// @param completionHandler 结果回调
- (void)setGATTProxyState:(uSDKMeshProxyFeatureState)state
               completion:(void(^_Nullable)(BOOL success, NSError *_Nullable error))completionHandler;



#pragma mark - Relay
//Relay and Relay Retransmit states.
typedef enum : uint8_t {
    uSDKMeshRelayFeatureState_supportedButDisabled,
    uSDKMeshRelayFeatureState_supportedAndEnabled,
    uSDKMeshRelayFeatureState_notSupported,
} uSDKMeshRelayFeatureState;

/// 获取当前设备的Relay and Relay Retransmit states.
/// @param completionHandler 结果回调
- (void)getRelayStateCompletion:(void(^_Nullable)(BOOL success, NSError *_Nullable error, uSDKMeshRelayFeatureState currentState))completionHandler;

/// 设置当前设备的Relay and Relay Retransmit states.
/// @param state Relay and Relay Retransmit states.
/// @param completionHandler 结果回调
- (void)setRelayState:(uSDKMeshRelayFeatureState)state
           completion:(void(^_Nullable)(BOOL success, NSError *_Nullable error))completionHandler;


@end

