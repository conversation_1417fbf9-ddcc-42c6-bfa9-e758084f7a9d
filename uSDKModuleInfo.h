//
//  uSDKModuleInfo.h
//  uSDK
//
//  Created by 王兵 on 2020/5/11.
//  Copyright © 2020 haier. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

/**
 设备模块信息
 @since 6.0.0
 */
@interface uSDKModuleInfo : NSObject

/**
 设备接入方式，可取值：
 1. unknown，未知接入方式
 2. wifimodule，WIFI模块
 3. blemodule，BLE模块
 4. blemeshmodule，BLE Mesh模块
 5. gprsmodule，GPRS模块
 6. nbiotmodule，NB-Iot模块
 @since 6.0.0
 */
@property (nonatomic, copy) NSString *deviceAccessType;

/**
 
 @since 6.0.0
 */
@property (nonatomic, copy) NSString *machineId;

/**
设备接入组件的硬件类型
@since 6.0.0
*/
@property (nonatomic, copy) NSString *hardWareType;

/**
设备接入组件的硬件版本号
@since 6.0.0
*/
@property (nonatomic, copy) NSString *hardWareVer;

/**
设备接入组件的软件类型
@since 6.0.0
*/
@property (nonatomic, copy) NSString *softWareType;

/**
设备接入组件的软件版本号
@since 6.0.0
*/
@property (nonatomic, copy) NSString *softWareVer;
@end

NS_ASSUME_NONNULL_END
