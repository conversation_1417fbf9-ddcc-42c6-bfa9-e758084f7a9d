//
//  uSDKRouterInfo.h
//  uSDK
//
//  Created by oet on 2021/8/4.
//  Copyright © 2021 haier. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface uSDKRouterInfo : NSObject
/**
 路由器的ssid
 */
@property (nonatomic, copy) NSString *SSID;
/**
 路由器的密码
 */
@property (nonatomic, copy) NSString *password;
/**
 路由器的bssid
 */
@property (nonatomic, copy) NSString *BSSID;
/**
 超时时间（单位是秒，范围为30秒-120秒），建议90秒
 */
@property (nonatomic, assign) NSTimeInterval timeoutInterval;

/**
 主网关域名
 @since 8.11.0
 */
@property (nonatomic, copy) NSString* mainGatewayDomain;

/**
 主网关端口
 @since 8.11.0
 */
@property (nonatomic, assign) NSInteger mainGatewayPort;

/**
 可取值 JP(日本) CN(中国) EU(欧洲) US(美国） WW(世界)
 @since 8.11.0
 */
@property (nonatomic, copy) NSString *country;

/**
 打点需要的CS节点
 */
@property (nonatomic,strong) uTraceNode *traceNodeCS;

@end

//UMesh配对信息类
@interface uSDKUMeshPairInfo : NSObject

/**
 设备类型标识
 @since 10.4.0
 */
@property (nonatomic, assign) NSInteger devType;
/**
 信号强度
 @since 10.4.0
 */
@property (nonatomic, assign) NSInteger rssi;
/**
 配对模式
 @since 10.4.0
 */
@property (nonatomic, assign) NSInteger mode;






@end


@interface uSDKUMeshPairDeviceInfo: NSObject

@property (nonatomic, copy) NSString *typeid;

@property (nonatomic, copy) NSString *devMac;

@property (nonatomic, copy) NSString *devId;
@end


@interface uSDKUMeshPairStateAndList : NSObject
@property (nonatomic, assign) NSInteger pairstate;
@property (nonatomic, strong) NSArray <uSDKUMeshPairDeviceInfo *> *list;
@end
NS_ASSUME_NONNULL_END
