//
//  uSDKQRAuthCodeInfo.h
//  uSDK
//
//  Created by 夏明伟 on 2022/12/7.
//  Copyright © 2022 haier. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface uSDKQRAuthCodeInfo : NSObject

/**
 扫码授权类型，字符串类型，可取值如下：
 QRCODE_LOGIN,       // 仅登录
 QRCODE_BIND,        // 仅绑定
 QRCODE_LOGIN_BIND,  // 登录且绑定
 */
@property (nonatomic, copy) NSString *QRCodeType;

/**
 设备typeId
 */
@property (nonatomic, copy) NSString *deviceTypeId;

/**
 设备成品编码
 */
@property (nonatomic, copy) NSString *deviceProductCode;

/**
 标记扫码授权类型是否存在
 0: 扫码授权类型不存在
 1: 扫码授权类型存在
 */
@property (nonatomic, assign) NSInteger QRAuthProcess;
@end

NS_ASSUME_NONNULL_END
