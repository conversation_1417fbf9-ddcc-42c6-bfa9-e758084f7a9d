//
//  QRCodeBindInfo.h
//  uSDK
//
//  Created by 李可 on 2018/10/17.
//  Copyright © 2018年 haier. All rights reserved.
//

#import <Foundation/Foundation.h>
@class uTraceNode;
#import "uSDKBaseBindInfo.h"

/**
 扫码绑定信息类
 */
@interface uSDKQRCodeBindInfo : uSDKBaseBindInfo

/**
 扫描二维码得到的信息，包含uplusID、deviceID、bindkey等
 */
@property (nonatomic,copy) NSString *qrCode;
/**
 打点需要的CS节点
 */
@property (nonatomic,strong) uTraceNode *traceNodeCS;
/**
 超时时间, 建议20-120s，默认60s
 */
@property (nonatomic,assign) NSTimeInterval timeoutInterval;

/**
 生成扫码绑定信息对象

 @param qrCode 二维码信息
 @param traceNodeCS CS节点信息
 @return 扫码绑定对象
 */
- (instancetype)initWithQrCode:(NSString *)qrCode
                   traceNodeCS:(uTraceNode *)traceNodeCS NS_DESIGNATED_INITIALIZER;
@end
