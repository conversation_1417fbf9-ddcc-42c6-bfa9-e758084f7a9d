//
//  uSDKConfigRouterInfo.h
//  查询配置的路由器信息：ssid、bssid、password
//
//
//  Created by oet on 2020/10/15.
//  Copyright © 2020 haier. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN
/**
 配置的路由器信息
 @since 8.0.0
*/
@interface uSDKConfigRouterInfo : NSObject

/**
 路由器ssid
 @since 8.0.0
*/
@property (nonatomic, copy) NSString *ssid;
/**
 路由器 bssid
 @since 8.0.0
*/
@property (nonatomic, copy) NSString *bssid;
/**
 路由器password
 @since 8.0.0
*/
@property (nonatomic, copy) NSString *password;
/**
 是否需要切网
 @since 8.0.0
*/
@property (nonatomic, assign) BOOL isNeedSwitchNetwork;

/**
 路由器ssid5G
 @since 9.0.0
*/
@property (nonatomic, copy) NSString *ssid5G;
/**
 路由器 bssid5G
 @since 9.0.0
*/
@property (nonatomic, copy) NSString *bssid5G;
/**
 路由器password5G
 @since 9.0.0
*/
@property (nonatomic, copy) NSString *password5G;
/**
 是否支持一键自动配网
 @since 9.0.0
*/
@property (nonatomic, assign) BOOL isOneKeyAutoConfigWiFi;

@end

NS_ASSUME_NONNULL_END
