//
//  uSDKBLEBindInfo.h
//  uSDK
//
//  Created by wangbing on 2018/7/6.
//  Copyright © 2018年 haier. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "uTrace.h"
#import "uSDKBaseBindInfo.h"

/**
 BLE设备绑定参数类，该类中提供BLE设备绑定时所需的参数
 */
@interface uSDKBLEBindInfo : uSDKBaseBindInfo

/**
 接入点WIFI名称，必填
 */
@property (nonatomic, copy) NSString *ssid;

/**
 接入点WIFI的bssid
 */
@property (nonatomic, copy) NSString *bssid;

/**
 接入点WIFI密码，支持无密码的情况，但如果WIFI有密码，则密码长度必须在[8,64]之间
 */
@property (nonatomic, copy) NSString *password;

/**
 超时时间（单位是秒，范围为30秒-180秒），默认90秒
 */
@property (nonatomic, assign) NSTimeInterval timeoutInterval;

/**
 App用来进行链式跟踪的CS点，该字段为选填
 */
@property (nonatomic, strong) uTraceNode *traceNodeCS;

/**
 主网关域名
 @since 8.6.3
 */
@property (nonatomic, copy) NSString* mainGatewayDomain;

/**
 主网关端口
 @since 8.6.3
 */
@property (nonatomic, assign) NSInteger mainGatewayPort;

/**
 可取值 JP(日本) CN(中国) EU(欧洲) US(美国） WW(世界)
 @since 8.6.3
 */
@property (nonatomic, copy) NSString *country;

- (BOOL)isValid:(NSError **)outError;
@end


/**
 用来快速构建uSDKBLEBindInfo类
 */
@interface uSDKBLEBindInfoBuilder : NSObject
@property (nonatomic, strong, readonly) uSDKBLEBindInfo *bindInfo;

@property (nonatomic, copy) uSDKBLEBindInfoBuilder *(^ssid) (NSString *ssid);
@property (nonatomic, copy) uSDKBLEBindInfoBuilder *(^bssid) (NSString *bssid);
@property (nonatomic, copy) uSDKBLEBindInfoBuilder *(^password) (NSString *password);
@property (nonatomic, copy) uSDKBLEBindInfoBuilder *(^timeoutInterval) (NSTimeInterval timeoutInterval);
@property (nonatomic, copy) uSDKBLEBindInfoBuilder *(^traceNodeCS) (uTraceNode *traceNodeCS);
@property (nonatomic, copy) uSDKBLEBindInfoBuilder *(^mainGatewayDomain) (NSString *mainGatewayDomain);
@property (nonatomic, copy) uSDKBLEBindInfoBuilder *(^mainGatewayPort) (NSInteger mainGatewayPort);
@property (nonatomic, copy) uSDKBLEBindInfoBuilder *(^country) (NSString *country);
@end

