//
//  uSDKNFCInfo.h
//  uSDK
//
//  Created by 赵睿 on 2021/1/25.
//  Copyright © 2021 haier. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

//NFC标签数据解析结果
@interface uSDKNFCInfo : NSObject

/// NFC标签序列号
@property (nonatomic, copy) NSString *NFCSerialNumber;

/// 设备MAC
@property (nonatomic, copy) NSString *MAC;

/// 设备ID
@property (nonatomic, copy) NSString *deviceID;

/// 设备成品编码
@property (nonatomic, copy) NSString *productCode;

/// 华为的PID
@property (nonatomic, copy) NSString *hwProductID;

///  选填
///  @note 最大长度为32字符，可取字符集[0-9A-Za-z]及中横线[-]
@property (nonatomic, copy) NSString *custom; //c

- (BOOL)isValidwithCustom:(NSString *)custom;
@end

NS_ASSUME_NONNULL_END
