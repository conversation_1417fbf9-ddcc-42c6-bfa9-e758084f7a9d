//
//  uSDKOTAStatusInfo.h
//  uSDK
//
//  Created by 李可 on 2019/8/14.
//  Copyright © 2019 haier. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN


/**
 设备（模块）升级进度信息
 */
@interface uSDKOTAStatusInfo : NSObject

/**
 升级进度阶段
 */
@property (nonatomic, assign, readonly) uSDKOTAStatus upgradeStatus;

/**
 升级进度百分比
 */
@property (nonatomic, assign, readonly) float upgradeProgress;

@end

NS_ASSUME_NONNULL_END
