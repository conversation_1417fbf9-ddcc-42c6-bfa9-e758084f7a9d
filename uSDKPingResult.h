//
//  uSDKPingResult.h
//  uSDKGeneral
//
//  Created by liugn on 2017/11/21.
//  Copyright © 2017年 beijinghaier. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "uSDKSinglePingResult.h"

/**
 网络状态测试结果
 */
@interface uSDKPingResult : NSObject{
}

/**
 测试的URL
 */
@property (nonatomic, strong, readonly) NSString *url;

/**
 发送数
 */
@property (nonatomic, assign) NSInteger sendCount;

/**
 接收数
 */
@property (nonatomic, assign) NSInteger receiveCount;

/**
 丢包率
 */
@property (nonatomic, assign, readonly) double packetLossRate;

/**
 最快（单位毫秒）
 */
@property (nonatomic, assign, readonly) NSTimeInterval fastestTimeCost;

/**
 最慢（单位毫秒）
 */
@property (nonatomic, assign, readonly) NSTimeInterval slowestTimeCost;

/**
 平均（单位毫秒）
 */
@property (nonatomic, assign, readonly) NSTimeInterval averageTimeCost;

/**
 包长度
 */
@property (nonatomic, assign, readonly) NSInteger packetLength;

/**
 错误信息
 */
@property (nonatomic, strong, readonly) NSError *error;

/**
 每次ping的结果
 */
@property (nonatomic, strong, readonly) NSMutableDictionary<NSNumber*,uSDKSinglePingResult*> *sequenceNumber2Result;

-(instancetype)initWithURL:(NSString*)url error:(NSError*)error;

//-(instancetype)initWithURL:(NSString*)url
//                 sendCount:(NSInteger)sendCount
//              receiveCount:(NSInteger)receiveCount
//            packetLossRate:(double)packetLossRate
//           fastestTimeCost:(NSInteger)fastestTimeCost
//           slowestTimeCost:(NSInteger)slowestTimeCost
//           averageTimeCost:(NSInteger)averageTimeCost
//              packetLength:(NSInteger)packetLength;

@end
