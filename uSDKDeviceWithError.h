//
//  uSDKDeviceWithError.h
//  uSDK
//
//  Created by like on 2021/6/9.
//  Copyright © 2021 haier. All rights reserved.
//

#import <Foundation/Foundation.h>
@class uSDKDeviceInfo;


///  蜂窝类设备绑定失败信息类
@interface uSDKDeviceWithError : NSObject
/// 绑定失败的设备信息
@property (nonatomic, strong) uSDKDeviceInfo *deviceInfo;
/// 绑定失败的错误原因
@property (nonatomic, strong) NSError *error;

/// 初始化方法
/// @param deviceInfo deviceInfo信息
/// @param error 错误信息
- (instancetype)initWithDeviceInfo:(uSDKDeviceInfo*)deviceInfo error:(NSError*)error;
@end


