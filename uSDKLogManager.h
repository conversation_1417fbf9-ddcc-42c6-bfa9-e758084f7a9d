//
//  uSDKLogManager.h
//  uSDK
//
//  Created by 王兵 on 2021/8/27.
//  Copyright © 2021 haier. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@protocol uSDKCommonLogReDirectProtocol;
typedef NS_ENUM(NSUInteger, uSDKCommonLogLevelConst);

@interface uSDKLogManager : NSObject
+ (instancetype)sharedLogManager;

@property (nonatomic, weak) id<uSDKCommonLogReDirectProtocol> delegate;
@property (nonatomic, assign) uSDKCommonLogLevelConst level;
@property (nonatomic, assign) BOOL isWriteToFile;
@end

NS_ASSUME_NONNULL_END
