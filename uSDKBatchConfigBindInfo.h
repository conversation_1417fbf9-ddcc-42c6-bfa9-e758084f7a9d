//
//  uSDKBatchConfigBindingInfo.h
//  uSDK
//
//  Created by 郭永峰 on 2023/12/19.
//  Copyright © 2023 haier. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "uTrace.h"
#import "uSDKBaseBindInfo.h"

NS_ASSUME_NONNULL_BEGIN

@class uSDKDeviceInfo;

/// wifi 批量绑定信息类
@interface uSDKBatchConfigBindInfo : uSDKBaseBindInfo

/// 需要批量绑定的对象列表 数量范围（0,30]
@property(nonatomic, strong) NSArray<NSString *> * devices;

/// 接入点WIFI名称，必填
@property(nonatomic, copy) NSString * ssid;

/// 接入点WIFI的bssid
@property(nonatomic, copy) NSString * bssid;

/// 接入点WIFI密码，支持无密码的情况，但如果WIFI有密码，则密码长度必须在[8,64]之间
@property(nonatomic, copy) NSString * password;

/// 蓝牙配绑并发数量，范围 [3, 7]， 默认3
@property(nonatomic, assign) NSInteger blePoolSize;

/// App用来进行链式跟踪的CS点，该字段为选填
@property (nonatomic, strong) uTraceNode *traceNodeCS;


@end

NS_ASSUME_NONNULL_END
