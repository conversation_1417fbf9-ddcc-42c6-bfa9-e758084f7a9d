//
//  uSDKDeviceVersionInfo.h
//  uSDK
//
//  Created by 夏明伟 on 2022/12/29.
//  Copyright © 2022 haier. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface uSDKDeviceVersionInfo : NSObject

/**
 设备typeId
 */
@property (nonatomic, copy) NSString *typeId;

/**
 设备整机固件类型
 */
@property (nonatomic, copy) NSString *fwType;

/**
 设备整机固件版本号
 */
@property (nonatomic, copy) NSString *fwVer;

/**
 是否支持OTA
 */
@property (nonatomic, assign) BOOL supportOTA;



@end

NS_ASSUME_NONNULL_END
