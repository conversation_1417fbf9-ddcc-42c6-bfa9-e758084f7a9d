//
//  uTrace.h
//  uSDKGeneral
//
//  Created by liugn on 2017/8/3.
//  Copyright © 2017年 beijinghaier. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "uTraceNode.h"

@class uTraceNodeDI;

#define BIND_BUSINESS_ID @"bind"

/**
 链式跟踪类（该类的对象代表一次完整的链式跟踪，由traceID唯一标识）
 注意：在使用链式跟踪之前需要先启动uAnalytics模块，代码为：[uAnalytics startWithAppId:APPID appKey:APPKEY];
 */
@interface uTrace : NSObject

/**
 链式跟踪的ID
 */
@property (nonatomic,copy,readonly) NSString *traceID;


/**
 创建DI链式跟踪起点（即生成traceID）

 @return 链式跟踪对象
 */
+ (uTrace*)createDITrace;

/**
 添加DI链式跟踪打点

 @param traceNodeDI DI节点信息
 @param error 错误信息
 @return 接口是否执行成功
 */
- (BOOL)addTraceNodeDI:(uTraceNodeDI*)traceNodeDI error:(NSError **)error;


/**
 根据业务ID创建一个新的跟踪链对象，如果传入的businessID重复，则会将之前创建的对象更新为一个全新的链式跟踪
 
 @param businessID 业务ID
 @return 新创建的跟踪链对象
 */
+ (uTrace*)createTraceWithBusinessID:(NSString*)businessID;


/**
 根据自定义traceId创建链式跟踪对象
 
 1.该方法针对App主动服务业务使用
 2.调用埋点方法时，bId的值：
    2.1对于DI点，bId = uTraceNodeDI.bId
    2.2对于CS/CR点，bId = businessID
 
 @param traceID 链式跟踪ID，不能为空，且要求为32个字符长度的uuid字符串
 @param businessID 业务ID，可以为空
 
 @return uTrace对象
 @since 8.1.0
 */
+ (uTrace *)createTraceWithTraceID:(NSString *)traceID businessID:(NSString *)businessID;

/**
 根据业务ID获取链式跟踪链对象
 
 @param businessID 业务ID
 @return uTrace对象
 */
+ (uTrace*)getTraceWithBusinessID:(NSString*)businessID;

/**
 添加发送请求的跟踪节点

 @param traceNode 节点信息
 @param error 错误信息
 @return 接口是否执行成功
 */
- (BOOL)addSendRequestTraceNode:(uTraceNode*)traceNode error:(NSError **)error;

/**
 添加收到回应的跟踪节点
 
 @param traceNode 节点信息
 @param relatedCSNode 关联的CS节点信息
 @param error 错误信息
 @return 接口是否执行成功
 */
- (BOOL)addReceiveResponseTraceNode:(uTraceNode*)traceNode relatedCSNode:(uTraceNode*)relatedCSNode error:(NSError **)error;

/**
 * 设置GIOSessionID
 *
 * @param gioSessionId 需要设置的GIOSessionID字段
 *
 * @since 8.16.0 新增设置GIOSessionID方法
 */
+ (void)setGIOSessionID:(NSString *)gioSessionId;

@end
