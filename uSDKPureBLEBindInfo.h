//
//  uSDKPureBLEBindInfo.h
//  uSDK
//
//  Created by wangbing on 2019/5/13.
//  Copyright © 2019 haier. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "uTraceNode.h"
#import "uSDKBaseBindInfo.h"

NS_ASSUME_NONNULL_BEGIN
/**
 纯BLE设备绑定参数类，该类中提供纯BLE设备绑定时所需的参数
 */
@interface uSDKPureBLEBindInfo : uSDKBaseBindInfo

/**
 通过uSDKDeviceScanner扫描到的蓝牙设备对象，device.configType & uSDKDeviceConfigTypeBLE == 1表示该设备支持蓝牙配网
 */
@property (nonatomic, strong) uSDKDeviceInfo *deviceInfo;

/**
 超时时间（单位是秒，范围为30秒-180秒），默认90秒
 */
@property (nonatomic, assign) NSTimeInterval timeoutInterval;

/**
 打点需要的CS节点
 */
@property (nonatomic,strong) uTraceNode *traceNodeCS;

@end

NS_ASSUME_NONNULL_END
