//
//  uSDKDeviceScanner.h
//  uSDKGeneral
//
//  Created by wangbing on 2017/6/22.
//  Copyright © 2017年 beijinghaier. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "uSDKDeviceInfo.h"

/**
 未开启的权限
 @since 8.0.0
 */
typedef NS_ENUM(NSUInteger, uSDKInvalidPermission) {
    /**
     蓝牙未开启
     @since 8.0.0
     */
    uSDKBleInvalid,
    /**
     WiFi网络未开启
     @since 8.0.0
     */
    uSDKNetWorkInvalid,
};


@protocol uSDKDeviceScannerDelegate;

/**
 该类用于自发现待配网设备，目前包括蓝牙和新直连设备
 
 如果调用了扫描可配置设备接口`startScanConfigurableDevice`，则可以通过实现`uSDKDeviceScannerDelegate`代理，来接收可配置设备的变化上报
     
    [[uSDKDeviceScanner sharedScanner].delegate = xxx;
    [[uSDKDeviceScanner sharedScanner] startScanConfigurableDevice];
 
 */
@interface uSDKDeviceScanner : NSObject

/**
 当前搜索到的可配置的蓝牙设备列表
 */
@property (nonatomic, copy, readonly) NSArray<uSDKDeviceInfo*> *discoverdDevices;

/**
 代理，设备列表发生改变时触发
 */
@property (nonatomic, weak) id<uSDKDeviceScannerDelegate> delegate;


/**
 单例

 @return 类对象
 */
+ (instancetype)sharedScanner;

/**
 开始发现可配置的设备
 */
+ (NSError *)startScanConfigurableDevice;

/**
 停止发现可配置的设备
 */
+ (NSError *)stopScanConfigurableDevice;

/**
 添加代理监听
 @param delegate 代理对象
 @since v8.8.0  添加一对多代理支持
 */
- (void)addDelegate:(id<uSDKDeviceScannerDelegate>)delegate;
/**
 移除代理监听
 @param delegate 代理对象
 @since v8.8.0  添加一对多代理支持
 */
- (void)removeDelegate:(id<uSDKDeviceScannerDelegate>)delegate;
/**
 判断是否包含
 @param delegate 代理对象
 @since v8.8.0  添加一对多代理支持
 */
- (BOOL)containsDelegate:(id<uSDKDeviceScannerDelegate>)delegate;

@end


/**
 代理，当扫描到的可配置设备列表发生变化或设备有属性更新时触发
 */
@protocol uSDKDeviceScannerDelegate <NSObject>

@optional

/**
 发现新增的可配置设备

 @param scanner uSDKDeviceScanner 对象
 @param device  增加的设备对象
 */
- (void)deviceScanner:(uSDKDeviceScanner*)scanner didFindNewDevice:(uSDKDeviceInfo *)device;

/**
 发现有减少的可配置设备
  
 @param scanner uSDKDeviceScanner 对象
 @param device  减少的设备对象
 */
- (void)deviceScanner:(uSDKDeviceScanner*)scanner didRemoveDevice:(uSDKDeviceInfo *)device;


/**
 发现可配置设备有属性更新

 @note 单纯RSSI的变化不会触发此接口
 @param scanner uSDKDeviceScanner 对象
 @param device 更新的设备对象
 */
- (void)deviceScanner:(uSDKDeviceScanner*)scanner didUpdateDevice:(uSDKDeviceInfo *)device;


/**
 监听到权限未开启，当关闭蓝牙或是关闭WIFI时会触发此回调

 @param scanner uSDKDeviceScanner 对象
 @param invalidPermission 未开启的权限
 @since 8.0.0
 */
- (void)deviceScanner:(uSDKDeviceScanner*)scanner didPermissionInvalid:(uSDKInvalidPermission )invalidPermission;


@end
