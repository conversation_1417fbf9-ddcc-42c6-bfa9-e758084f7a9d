//
//  uSDKNFCUtil.h
//  uSDK
//
//  Created by 赵睿 on 2021/1/25.
//  Copyright © 2021 haier. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "uSDKNFCInfo.h"

NS_ASSUME_NONNULL_BEGIN

@interface uSDKNFCUtil : NSObject

/**
 *  解析NFC标签数据
 *  @param NdefRecord   NFC标签原始数据
 *  @param complete    接口回调 成功返回uSDKNFCInfo类，失败返回error
 */
+ (void)parseNFCTagDataWithNdefRecord:(NSString *)NdefRecord
                             complete:(void(^)(uSDKNFCInfo *NFCInfo, NSError *error))complete;

/**
 *  更新NFC设备信息
 *  @param NFCInfo    NFC标签解析后数据
 *  @param complete    接口回调 失败返回error
 */
+ (void)updateNFCDeviceInfoWithNFCInfo:(uSDKNFCInfo *)NFCInfo
                              complete:(void(^)(NSError *error))complete;

@end

NS_ASSUME_NONNULL_END
