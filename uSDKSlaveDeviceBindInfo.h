//
//  uSDKSlaveDeviceBindInfo.h
//  uSDK
//
//  Created by wangbing on 2019/11/22.
//  Copyright © 2019 haier. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "uSDKDevice.h"
#import "uSDKBaseBindInfo.h"

NS_ASSUME_NONNULL_BEGIN

/**
网关子机绑定信息类
*/
@interface uSDKSlaveDeviceBindInfo : uSDKBaseBindInfo
/**
网关设备对象，必填
@warning 该对象需要使用从uSDK中返回的设备对象，不能自己创建
*/
@property (nonatomic, strong) uSDKDevice *masterDevice;
/**
 * 设备的uplusID
 *
 * @note v8.11.0开始，uplusID在批量绑定接口调用时为非必填。在单个子机设备绑定接口调用时为必填。
*/
@property (nonatomic, copy) NSString *uplusID;
/**
 * 设备的产品编码
 *
 * @note v8.11.0开始，productCode 在批量绑定接口调用时为非必填。在单个子机设备绑定接口调用时为必填。
*/
@property (nonatomic, copy) NSString *productCode;
/**
超时时间，单绑定30-120s，批量绑定30-600s，默认60s
*/
@property (nonatomic, assign) NSTimeInterval timeoutInterval;

/**
 * 打点需要的CS节点
 */
@property (nonatomic, strong) uTraceNode *traceNodeCS;

/**
 通用扩展信息，可以为空，SDK不做校验，仅透传

 如：RISCO设备接入时，需传入扩展信息：ip/port/Identification Code，信息格式需App开发者与设备侧协商。
 @since 8.1.0

*/
@property (nonatomic, copy) NSString *extendInfo;

@end

NS_ASSUME_NONNULL_END
