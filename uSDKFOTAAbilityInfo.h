//
//  uSDKFOTAAbilityInfo.h
//  uSDK
//
//  Created by 夏明伟 on 2022/1/26.
//  Copyright © 2022 haier. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN
/**
 * 设备FOTA能力
 */
@interface uSDKFOTAAbilityInfo : NSObject
//FOTA的通道: 0-NULL;1-WiFi;2-BLE
@property (nonatomic, assign) uSDKFOTAChannel FOTAChannel;
//FOTA的功能版本: 0-FOTA通道为NULL；1-FOTA通道为WIFI时/FOTA通道为BLE旧广播版本时；2-FOTA通道为BLE新广播版本时
@property (nonatomic, assign) NSInteger FOTAFunctionVersion;
//设备的连接状态: 0-UNCONNECT；1-OFFLINE；2-CONNECTING；3-CONNECTED；4-READY)
@property (nonatomic, assign) uSDKDeviceState connectionState;
//设备的睡眠状态: 0-Unsleeping；1-Sleeping；2-WakingUp
@property (nonatomic, assign) uSDKDeviceSleepState sleepState;




@end

NS_ASSUME_NONNULL_END
