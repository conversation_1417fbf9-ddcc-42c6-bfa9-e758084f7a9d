//
//  uTraceNodeDI.h
//  uSDKGeneral
//
//  Created by liugn on 2018/3/2.
//  Copyright © 2018年 beijinghaier. All rights reserved.
//

#import <Foundation/Foundation.h>
@class uTraceNode;


/**
 链式跟踪DI点
 */
@interface uTraceNodeDI : NSObject

/**
 关键业务代码，控制业务为定值 op，控制应答为:opack
 */
@property (nonatomic,copy) NSString *bId;
/**
 云平台用户 Id
 */
@property (nonatomic,copy) NSString *uId;
/**
 网器 MAC
 */
@property (nonatomic,copy) NSString *dId;
/**
 所属系统，枚举: MODULE、USDK、APP、UWS、m2m、AGS 等
 */
@property (nonatomic,copy) NSString *sys;
/**
 返回码
 */
@property (nonatomic,copy) NSString *code;
/**
 接口名称或URL
 */
@property (nonatomic,copy) NSString *bName;
/**
 协议类型，枚举:http、dubbo、db、kafka、rpc、local、thrift、 mqtt、wifi、uwt、coap。
 */
@property (nonatomic,copy) NSString *prot;
/**
 APP、USDK、云平台和 APPSERVER 的版本信息，各系统根据自己情 况填写。如:3.4.0 3.4.1
 */
@property (nonatomic,copy) NSString *ver;
/**
 返回为非 00000 时，记录返回信息
 */
@property (nonatomic,copy) NSString *desc;
/**
 Header 参数，json 格式，适用于 http 协议，非 http 协议不计。
 */
@property (nonatomic,copy) NSString *rqHd;
/**
 异常描述，异常时记录
 */
@property (nonatomic,copy) NSString *exp;
/**
 请求参数 json 格式。
 */
@property (nonatomic,copy) NSString *ipm;
/**
 正常响应内容 json
 */
@property (nonatomic,copy) NSString *rrt;
/**
 从收到控制指令到发送响应或从发送指令到收到响应之间的耗时。
 */
@property (nonatomic,assign) UInt32 span;
/**
 扩展信息（args 中的pipe、ntype、ver、opno）
 */
@property (nonatomic,strong) NSMutableDictionary *args;
/**
用户token
*/
@property (nonatomic,copy) NSString *tk;

/**
 步骤ID
 */
@property (nonatomic,copy) NSString *uSpanId;

/**
 子系统名称
 */
@property (nonatomic,copy) NSString *subSys;

/**
 快速初始化方法

 @param pipe 控制通道
 @param bId 关键业务代码
 @param sys 所属系统
 @param code 返回码
 @param uId 云平台用户ID
 @param dId 设备ID
 @param bName 接口名称
 @param prot 协议类型
 @param ver 版本信息
 @return uTraceNodeDI实例对象
 */
-(instancetype)initWithPipe:(NSString*)pipe bId:(NSString *)bId sys:(NSString *)sys code:(NSString *)code uId:(NSString *)uId dId:(NSString *)dId bName:(NSString *)bName prot:(NSString *)prot ver:(NSString *)ver;

@end
