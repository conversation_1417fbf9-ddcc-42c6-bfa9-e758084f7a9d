//
//  uSDKDeviceManager.h
//  uSDK_iOS_v2
//
//  Created by <PERSON><PERSON> on 14-1-7.
//  Copyright (c) 2014年 haierubic. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "uSDKConstantInfo.h"
#import "uSDKDevice.h"
#import "uSDKDeviceConfigInfo.h"
#import "uSDKSoftApConfigInfo.h"
#import "uTraceNode.h"
#import "uSDKUserInfo.h"
#import "uSDKDeviceRegisterInfo.h"
#import "uSDKPrepareDevInfo.h"

@protocol  uSDKDeviceManagerDelegate;
@protocol  uSDKDeviceRegisterDelegate;
@protocol  uSDKDeviceUnRegisterDelegate;

/**
 *  uSDK设备管理单例类，用于管理uSDK自动发现的局域网内设备和用户关联的远程设备。
 */
@interface uSDKDeviceManager : NSObject {
    uSDKCloudConnectionState _cloudConnectionState;
}

/**
 *  关心的设备类型。用于过滤deviceManager:didAddDevices:回调和deviceManager:didRemoveDevices:回调返回的设备列表，若未设置，则返回所有类型设备
 */
@property (nonatomic, strong) NSArray<NSNumber*> *interestedDeviceTypes;

/**
 *  云连接状态
 */
@property (nonatomic, assign, readonly) uSDKCloudConnectionState cloudConnectionState;

/**
 *  代理，用于接收设备列表变化、绑定、解绑、云平台连接状态消息
 *  注意:
 *  需先设置感兴趣的设备类型，若不设置则默认订阅所有类型设备
 *  见 interestedDeviceTypes 属性
 */
@property (nonatomic, weak) id<uSDKDeviceManagerDelegate> delegate;

/**
 *  代理，用于接收设备登记的消息
 */
@property (nonatomic, weak) id<uSDKDeviceRegisterDelegate> registerDelegate;

/**
 *  代理，用于接收设备取消登记的消息
 */
@property (nonatomic, weak) id<uSDKDeviceUnRegisterDelegate> unregisterDelegate;

/**
 *  所有已发现的设备。这些设备包括uSDK自动发现的wifi网络设备和已与用户关联的设备。如果设备既能本地访问也能远程访问，
 *  uSDK访问设备时会优先本地访问。
 *  设备列表存储形式：[{Key: 家电设备ID, Object: 家电设备实例}, {}...]；
 */
@property (nonatomic, strong ,readonly) NSDictionary<NSString*,uSDKDevice*>* deviceDict;


/**
 *  获取uSDKDeviceManager单例，此单例用于访问自动发现的设备列表和与用户关联的设备列表。
 *
 *  @return 返回uSDKDeviceManager单例
 */
+ (uSDKDeviceManager*)defaultDeviceManager;

/**
 *  获取当前uSDK管理的设备列表。
 *
 *	@return	当前uSDK管理的设备列表
 */
- (NSArray<uSDKDevice*>*)getDeviceList;

/**
 *  根据类型获取当前uSDK管理的设备列表。
 *
 *  @param deviceType 根据设备类型筛选结果
 *
 *  @return 通过类型筛选后的设备列表
 */
- (NSArray<uSDKDevice*>*)getDeviceList:(uSDKDeviceTypeConst)deviceType;

/**
 *  根据设备ID获取指定设备
 *
 *  @param deviceID 设备ID
 *
 *  @return 根据设备ID得到的设备对象
 */
- (uSDKDevice*)getDeviceWithID:(NSString*)deviceID;


//设置用户预加载的设备列表
- (void)setPrepareDeviceList:(uSDKPrepareDevInfo *)prepareDevInfo;


/**
 *  更新用户相关信息，接口支持重复调用。
 *
 *  返回值：
 *  1. 如果SDK未启动，会返回error: ERR_USDK_UNSTARTED
 *  2. 如果userInfo ！= nil, 但userID或userToken为空，会返回error:ERR_USDK_INVALID_PARAM
 *  3. 其他情况均返回YES
 *
 *  关于云连接逻辑描述：
 *  1. uSDK启动后自动创建云连接，并负责云连接的重连
 *  2. 如果首次调用该方法，传入非空userInfo，会根据入参的userToken获取用户所有远程设备
 *  3. 如果重复调用，新传入的token与原token相同，则直接返回YES
 *  4. 如果要更新用户token，可直接重复调用，传入的userID要与之前保持一致，token为新值即可。
 *  5. 如果要注销用户，入参userInfo 传 nil即可
 *  6. 如果要切换用户
 *     6.1 可以调用两遍该接口，第一次调用时走5的逻辑，第二次调用时走2的逻辑
 *     6.2 也可以直接调用该接口，userInfo中传入新的userID和userToken即可
 *  7. 无论切换用户，还是更新用户token，或是注销，都不会断开云连接，即App无需关心云连接问题
 *
 *  @param userInfo 当userInfo != nil时，userToken和userID均不能为空
 *  @param error 接口执行失败的错误原因，（SDK未启动，或 userInfo对象中userToken为空 都会导致接口执行失败）
 *  @return 接口执行的结果
 *  @since 8.7.0
 */
- (BOOL)setUserInfo:(uSDKUserInfo *)userInfo error:(NSError **)error;

/**
 *  开启蓝牙搜索。
 *
 *  @param success      命令执行成功回调
 *  @param failure      命令执行失败回调，参数为错误原因
 */
- (void)startBleSearch:(void(^)(void))success
               failure:(void(^)(NSError *error)) failure;

/**
 *  停止蓝牙搜索。
 *
 *  @param success      命令执行成功回调
 *  @param failure      命令执行失败回调，参数为错误原因
 */
- (void)stopBleSearch:(void(^)(void))success
              failure:(void(^)(NSError *error)) failure;


/**
 *  开启wifi小循环搜索。
 *
 *  @param success      命令执行成功回调
 *  @param failure      命令执行失败回调，参数为错误原因
 *  @since 9.13.0
 */
- (void)startWifiSearch:(void(^)(void))success
                failure:(void(^)(NSError *error)) failure;


/**
 SoftAp配置接口，开发者无需再调用getSoftapDeviceConfigInfo接口。该接口整合了获取配置信息(getSoftapDeviceConfigInfo)和发送配置命令两个步骤，并且两个步骤均提供了重试功能，方便开发者使用。该接口可在softApConfigInfo参数中配置超时时间在APP进入后台时是否计时，默认APP进入后台计时。
 uSDK 4.5.01新增

 @param softApConfigInfo uSDKSoftApConfigInfo对象，在此对象中设置SSID、密码、超时时间等参数
 @param sendConfigInfoSuccess 向设备发送配置信息成功回调
 @param traceNodeCS 关联CS节点对象，如果APP有链式埋点，则需要APP将关联的CS节点对象传给uSDK
 @param success 成功回调
 @param failure 失败回调
 */
- (void)configDeviceBySoftapWithConfigInfo:(uSDKSoftApConfigInfo *)softApConfigInfo
                     sendConfigInfoSuccess:(void(^)(void)) sendConfigInfoSuccess
                               traceNodeCS:(uTraceNode*)traceNodeCS
                                   success:(void(^)(uSDKDevice *device)) success
                                   failure:(void(^)(NSError *error)) failure;


/**
 SmartLink模式配置设备接口(自定义超时时间和可指定deviceID和uplusID和是否为安全配置)
 
 @param ssid 接入点WIFI名称
 @param password 接入点WIFI密码
 @param deviceID 设备ID，目前均为设备MAC地址（MAC为无分隔符的字符串）
 @param uplusIDList 设备类型ID数组
 @param timeoutInterval 超时时间（单位是秒，范围为30秒-120秒）
 @param security 配置是否为安全配置
 @param traceNodeCS 链式跟踪APP创建的CS节点对象
 @param success 命令执行成功回调，参数为配置成功的设备
 @param failure 命令执行失败回调，参数为错误原因
 */
- (void)configDeviceBySmartLinkWithSSID:(NSString*)ssid
                               password:(NSString*)password
                               deviceID:(NSString*)deviceID
                            uplusIDList:(NSArray<NSString*>*)uplusIDList
                        timeoutInterval:(NSTimeInterval)timeoutInterval
                               security:(BOOL)security
                            traceNodeCS:(uTraceNode*)traceNodeCS
                                success:(void(^)(uSDKDevice *device)) success
                                failure:(void(^)(NSError *error)) failure;

/**
 *  获取Smartlink配置错误信息。
 *
 *  @param success 成功
 *  @param failure 失败
 */
-(void)getSmartLinkConfigErrorInfoWithSuccess:(void(^)(NSError *errorInfo)) success
                                      failure:(void(^)(NSError *error)) failure;

/**
 *  停止smartLink配置。如果用户需要取消配置则App需要调用此接口取消正在配置的任务。
 *
 *  @param success 成功回调
 *  @param failure 失败回调
 */
- (void)stopSmartLinkConfig:(void(^)(void))success
                    failure:(void(^)(NSError *error)) failure;




/**
 获取授权设备列表

 @param success 成功回调的数组，数组中存放的uSDKDevice对象
 @param failure 失败的回调
 */
- (void)getAuthDeviceListSuccess:(void(^)(NSArray<uSDKDevice*>*authDevList))success
                         failure:(void(^)(NSError *error)) failure;


/**
 会重试的绑定设备

 @param device 要绑定的设备
 @param deviceName 设备名称
 @param timeoutInterval 超时时间（20-120秒），推荐60秒
 @param traceNodeCS 链式跟踪关联的CS节点
 @param success 成功回调
 @param failure 失败回调
 */
- (void)bindDevice:(uSDKDevice*)device
        deviceName:(NSString *)deviceName
   timeoutInterval:(NSTimeInterval)timeoutInterval
       traceNodeCS:(uTraceNode*)traceNodeCS
           success:(void(^)(void))success
           failure:(void(^)(NSError *error))failure;

/**
 停止绑定设备

 @param device 要停止的设备
 @param error 错误信息
 */
- (void)stopBindDevice:(uSDKDevice*)device error:(NSError**)error;


/**
 解绑设备

 @param device 要解绑的设备
 @param timeoutInterval 超时时间（20-120秒）
 @param success 成功回调
 @param failure 失败回调
 */
- (void)unbindDevice:(uSDKDevice*)device
     timeoutInterval:(NSTimeInterval)timeoutInterval
             success:(void(^)(void))success
             failure:(void(^)(NSError *error))failure;


/**
 刷新已绑定的设备列表，成功只表示命令发送成功，云平台收到请求后，会下发最新的绑定设备列表，如果与USDK内部缓存的列表有变化（增加或减少），则通过代理方法deviceManager:didAddDevices:和deviceManager:didRemoveDevices:通知App

 @param success 命令发送成功的回调
 @param failure 命令发送失败的回调
 */
- (void)refreshDeviceListWithSuccess:(void(^)(void))success
                             failure:(void(^)(NSError *error))failure;



/// 进入产测模式
/// @param deviceTmpId  设备临时id
/// @param delegate  接收产测的代理对象
/// @param success  成功回调，包含对应的device对象
/// @param failure  失败回调
- (void)enterProductionTestMode:(NSString *)deviceTmpId delegate:(id<uSDKDeviceDelegate>)delegate success:(void(^)(uSDKDevice *device))success failure:(void(^)(NSError *error))failure;


/// 退出产测模式
/// @param device  退出产测的设备对象
/// @param success  成功回调
/// @param failure  失败回调
- (void)exitProductionTestMode:(uSDKDevice *)device success:(void(^)(void))success failure:(void(^)(NSError *error))failure;

/**
 添加代理监听
 
 @param  delegate  需要添加的代理对象
 @since v8.8.0  添加一对多代理支持
 */
- (void)addDelegate:(id<uSDKDeviceManagerDelegate>)delegate;

/**
 移除代理监听
 @param delegate 代理对象
 @since v8.8.0  添加一对多代理支持
 */
- (void)removeDelegate:(id<uSDKDeviceManagerDelegate>)delegate;
/**
 判断是否包含
 @param delegate 代理对象
 @since v8.8.0  添加一对多代理支持
 */
- (BOOL)containsDelegate:(id<uSDKDeviceManagerDelegate>)delegate;

@end


/**
 *  uSDKDeviceManager对象的代理协议，包括设备增加、删除、绑定、解绑及云连接状态消息。
 */
@protocol uSDKDeviceManagerDelegate <NSObject>

@optional

/**
 *  发现设备消息（该消息为实时消息）
 *
 *  @param deviceManager 设备管理器对象
 *  @param devices       新增的设备数组
 */
-(void)deviceManager:(uSDKDeviceManager*)deviceManager didAddDevices:(NSArray<uSDKDevice*>*)devices;

/**
 *  删除设备消息（该消息为实时消息）
 *
 *  @param deviceManager 设备管理器对象
 *  @param devices       删除的设备数组
 */
-(void)deviceManager:(uSDKDeviceManager*)deviceManager didRemoveDevices:(NSArray<uSDKDevice*>*)devices;

/**
 *  设备绑定
 *
 *  @param deviceManager 设备管理器对象
 *  @param deviceID   绑定设备的ID
 */
-(void)deviceManager:(uSDKDeviceManager*)deviceManager didBindDevice:(NSString*)deviceID;

/**
 *  设备解绑定
 *
 *  @param deviceManager 设备管理器对象
 *  @param deviceID   解绑定设备的ID
 */
-(void)deviceManager:(uSDKDeviceManager*)deviceManager didUnbindDevice:(NSString*)deviceID;

/**
 *  云连接状态更新
 *
 *  @param deviceManager 设备管理器对象
 *  @param state         云平台连接状态
 *  @param offlineReason 离线原因
 */
-(void)deviceManager:(uSDKDeviceManager*)deviceManager didUpdateCloudState:(uSDKCloudConnectionState)state error:(NSError*)offlineReason;

@end


/**
 *  uSDKDeviceManager对象的代理协议，包括设备登记的消息。
 */
@protocol uSDKDeviceRegisterDelegate <NSObject>

@optional
/**
 *  设备登记
 *
 *  @param deviceManager 设备管理器对象
 *  @param registerInfo   设备登记的消息
 */
-(void)deviceManager:(uSDKDeviceManager*)deviceManager didRegisterDevice:(uSDKDeviceRegisterInfo*)registerInfo;

@end


/**
 *  uSDKDeviceManager对象的代理协议，包括设备取消登记的消息。
 */
@protocol uSDKDeviceUnRegisterDelegate <NSObject>

@optional
/**
 *  设备取消登记
 *
 *  @param deviceManager 设备管理器对象
 *  @param unRegisterInfo   设备取消登记的消息
 */
-(void)deviceManager:(uSDKDeviceManager*)deviceManager didUnregisterDevice:(uSDKDeviceRegisterInfo*)unRegisterInfo;


@end

