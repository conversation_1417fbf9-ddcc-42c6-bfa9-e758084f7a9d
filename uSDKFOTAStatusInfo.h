//
//  uSDKFOTAStatusInfo.h
//  uSDK
//
//  Created by 李可 on 2019/5/15.
//  Copyright © 2019 haier. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

/**
 设备升级进度信息
 */
@interface uSDKFOTAStatusInfo : NSObject

/**
 设备升级进度,可取值为0，100,101,102,103,104,105，具体代表的含义如下
 
 0:升级完成，（有可能升级成功，也有可能是失败， 需要判断 upgradeErrNo 字段， 如果该字段为nil 在代表升级成功，反之则为失败。）
 100:设备当前未在升级，仅在用户主动查询设备固件升级进度时返回；
 101:升级指令正在下发给设备，仅在用户主动查询设备固件升级进度时返回；
 102:升级指令已下发给设备，正在下载升级包，设备可以立即升级；
 103:升级指令已下发给设备，正在下载升级包，但设备当前不适合升级，稍后会在适合的时机自动升级完成；
 104:升级包已下载完成，但设备当前不适合升级，稍后会在适合的时机自动升级完成；
 105:升级包已下载完成，设备正在升级中；
 */
@property (nonatomic, assign) NSInteger upgradeStatus;

/**
 失败的错误原因
 */
@property (nonatomic, strong) NSError *upgradeErrorInfo;


@property (nonatomic, copy) NSString *traceID;

@property (nonatomic, copy) NSString *firmwareID;

@property (nonatomic, assign) uSDKDeviceState connectionState;

@property (nonatomic, copy) NSString *firmwareType;

@property (nonatomic, copy) NSString *firmwareVersion;

/**
 大固件升级，固件包下载进度
 
 @since 9.4.0
 */
@property (nonatomic, assign) NSInteger downloadProgress;

@end

NS_ASSUME_NONNULL_END
