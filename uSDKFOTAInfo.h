//
//  uSDKFOTAInfo.h
//  uSDK
//
//  Created by 李可 on 2019/5/10.
//  Copyright © 2019 haier. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

/**
 设备底板固件升级信息类
 */
@interface uSDKFOTAInfo : NSObject

/**
 设备是否需要升级
 */
@property (nonatomic, assign, readonly) BOOL isNeedFOTA;

/**
 当前版本号
 */
@property (nonatomic, copy, readonly) NSString *currentVersion;

/**
 最新版本号
 */
@property (nonatomic, copy, readonly) NSString *newestVersion;

/**
 最新版本号的描述
 */
@property (nonatomic, copy, readonly) NSString *newestVersionDescription;

/**
 U+产品整机型号编码
 */
@property (nonatomic, copy, readonly) NSString *model;

/**
 升级的超时时间
 */
@property (nonatomic, assign, readonly) NSTimeInterval timeoutInterval;

@end


/**
 设备整机固件升级信息类
 */
@interface uSDKDeviceFOTAInfo : NSObject

/**
 链式跟踪打点，全流程打点唯一标识
 */
@property (nonatomic, copy) NSString *traceID;

/**
 整机固件唯一标识
 */
@property (nonatomic, copy) NSString *firmwareID;

@end

NS_ASSUME_NONNULL_END
